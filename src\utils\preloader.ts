// Preload critical resources
export const preloadCriticalResources = () => {
  // Preload hero images
  const heroImages = [
    '/src/assets/images/logo-white.avif',
    '/src/assets/images/background.avif'
  ];

  heroImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });

  // Preload critical routes
  import('../pages/Home').then(() => {
    console.log('Home component preloaded');
  });
};

// Prefetch non-critical resources
export const prefetchResources = () => {
  const routes = [
    () => import('../pages/Wallet'),
    () => import('../pages/Shop'),
    () => import('../pages/Map')
  ];

  // Prefetch after initial load
  setTimeout(() => {
    routes.forEach(route => route());
  }, 2000);
};