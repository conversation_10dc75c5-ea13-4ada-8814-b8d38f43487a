import { FC, useEffect, useRef, useState } from 'react';
import { IMAGES } from '../../constant/image';

type LoadingIndexes = '1' | '2' | '3' | '4' | '5';

const SPINNER_TICK_TIME = 500;
const LOADING_COUNT_TIME = 30;
const FULL_PERCENTAGE = 100;
const FAILED_MESSAGE_TIMEOUT = 2000;

export const GameLoadingScreen: FC = () => {
  const spinnerIntervalRef = useRef<number>(0);
  const loadingProgressIntervalRef = useRef<number>(0);
  const loadingImgRef = useRef<HTMLImageElement>(null);
  const bgIndex = useRef<string>(String(Math.ceil((Math.random() / 2) * 10)));

  const [spinnerIndex, setSpinnerIndex] = useState<string>('1');
  const [percentage, setPercentage] = useState<number>(0);
  const [loadingFailed, setLoadingFailed] = useState<boolean>(false);
  // These markers are using to show spinner image and text in a correct order - background image - spinner - text
  const [showInnerContent, setShowInnerContent] = useState<boolean>(false);

  const failMessageStyle = {
    blockStyle: {
      fontFamily: 'Anton',
      WebkitTextStroke: '2px rgb(216, 0, 0)',
      WebkitTextFillColor: 'white',
    },
    percentageStyle: 'text-red-500',
    percentageValue: 'FAILED',
    percentageNode: <></>,
  };

  const regMessageStyle = {
    blockStyle: {
      fontFamily: 'Anton',
      WebkitTextStroke: '2px #191B1F',
      WebkitTextFillColor: 'white',
    },
    percentageStyle: 'w-[50px]',
    percentageValue: percentage,
    percentageNode: <span className="ml-1">%</span>,
  };

  const messageStyle = loadingFailed ? failMessageStyle : regMessageStyle;

  const clearIntervals = () => {
    clearInterval(loadingProgressIntervalRef.current);
    clearInterval(spinnerIntervalRef.current);
  };

  useEffect(() => {
    spinnerIntervalRef.current = window.setInterval(() => {
      setSpinnerIndex((prevIndex) => {
        const convPrevIndex = Number(prevIndex);
        return String(convPrevIndex === 5 ? 1 : convPrevIndex + 1);
      });
    }, SPINNER_TICK_TIME);

    loadingProgressIntervalRef.current = window.setInterval(
      () => setPercentage((prevPercentage) => prevPercentage + 1),
      LOADING_COUNT_TIME
    );
    setTimeout(() => setShowInnerContent(true), SPINNER_TICK_TIME * 2);

    return () => clearIntervals();
  }, []);

  useEffect(() => {
    if (percentage === FULL_PERCENTAGE) {
      clearIntervals();
      setTimeout(() => setLoadingFailed(true), FAILED_MESSAGE_TIMEOUT);
    }
  }, [percentage]);

  return (
    <div
      className="game-loading-screen flex justify-center items-center fixed z-50 touch-none top-0 left-0 mt-2 sm:max-w-md sm:left-auto sm:mx-auto"
      style={{
        backgroundImage: `url(${
          IMAGES[`LOADING_BG_${bgIndex.current as LoadingIndexes}`]
        })`,
        backgroundSize: 'cover',
        borderRadius: '30px',
        width: '96vw',
        height: '98vh',
        left: '50%',
        transform: 'translateX(-50%)',
      }}
    >
      {showInnerContent && (
        <div>
          <img
            src={IMAGES[`SPINNER_${spinnerIndex as LoadingIndexes}`]}
            ref={loadingImgRef}
          />
          <div
            className="text-center text-4xl w-full mt-2"
            style={messageStyle.blockStyle}
          >
            <span className={`inline-block ${messageStyle.percentageStyle}`}>
              {messageStyle.percentageValue}
            </span>
            {messageStyle.percentageNode}
          </div>
        </div>
      )}
    </div>
  );
};
