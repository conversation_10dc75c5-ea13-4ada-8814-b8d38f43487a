/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {

      colors: {
        'fortune-start': '#1a1a1a',  // very dark near-black
        'fortune-end': '#4b442a',    // muted olive green
      },
      backgroundImage: {
        'fortune-gradient': 'linear-gradient(180deg, #1a1a1a 0%, #4b442a 100%)',
      },
      fontFamily: {
        oswald: ['Oswald', 'sans-serif'],
        haydes: ["'Haydes'", "sans-serif"],
      },
      fontSize: {
        '2.5xl': '1.75rem', // 28px (between 2xl [1.5rem] and 3xl [1.875rem])
      },
      dropShadow: {
        'text-outline': [
          '0 0 1px rgba(0,0,0,1)',
          '0 0 1px rgba(0,0,0,1)',
        ],
      },
      colors: {
        'surface-light': '#1a1b1e',
        'surface-card': '#25262b',
        'text-primary': '#ffffff',
        'text-muted': '#909296',
        'primary': '#ED0CFF',
        'secondary': '#34d399',
        'accent-gold': ' #ED0CFF',
        'accent-blue': '#60a5fa',
        'accent-purple': '#8b5cf6',
        'error': '#ef4444',
        'gradient-start': '#6d28d9',
        'gradient-end': '#4c1d95'
      }
    },
  },
  plugins: [],
};