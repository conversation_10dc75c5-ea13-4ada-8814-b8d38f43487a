import axios from 'axios';
import { GET_WHEEL_CONFIG, GET_WHEEL_PRICE, GET_WHEEL_SPINNING } from './auth';
// src/services/spinningWheelService.ts

export interface SpinningWheelConfig {
  id: string;
  name: string;
  status: string;
  amount: string;
  type: string;
  frequency: number;
  icon: string;
  color: 'blue' | 'black' | 'yellow' | 'violet' | 'white';
}

interface ApiResponse {
  Message: string;
  configs: SpinningWheelConfig[];
  total_page: number;
}

export interface SpinningWheelResponse {
  data: {
    id: string;
    bet_amount: string;
    prize: string;
  };
  message: string;
}

export const getSpinningWheelConfigs = async (
  page: number = 1,
  perPage: number = 10,
  token: string
): Promise<SpinningWheelConfig[]> => {
  const response = await axios.get<ApiResponse>(
    `${GET_WHEEL_CONFIG}?page=${page}&per_page=${perPage}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data.configs;
};

export const getWheelPrice = async (token: string) => {
  const response = await axios.get(GET_WHEEL_PRICE, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
};

export const fetchSpinningWheelConfig = async (
  token: string
): Promise<SpinningWheelResponse> => {
  const response = await axios.post<SpinningWheelResponse>(
    GET_WHEEL_SPINNING,
    null,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  return response.data;
};
