# Betting Game Playzuzu

A React-based betting game application.

## Folder Structure

```
betting-game-playzuzu/
├── node_modules/
├── public/
│   ├── index.html
│   └── vite.svg
├── src/
│   ├── assets/
│   ├── api/
│   │   ├── airTimeService.ts
│   │   ├── auth.ts
│   │   └── scratchCardService.ts
│   ├── auth/
│   │   └── AuthContext.tsx
│   ├── components/
│   │   ├── BoostModal.tsx
│   │   ├── ForgotPasswordForm.tsx
│   │   ├── HelpSupport.tsx
│   │   ├── Layout.tsx
│   │   ├── Messages.tsx
│   │   ├── ProtectedRoute.tsx
│   │   ├── ScratchCard.tsx
│   │   ├── Settings.tsx
│   │   ├── Wallet.tsx
│   │   ├── WheelOfFortune.tsx
│   │   ├── ZendeskWidget.tsx
│   │   ├── authGoogle.tsx
│   │   └── ...
│   ├── context/
│   │   ├── socketProvider.tsx
│   │   └── ...
│   ├── hooks/
│   │   ├── useFetch.ts
│   │   ├── useCoinsQuery.tsx
│   │   ├── useWebSocket.ts
│   │   └── ...
│   ├── pages/
│   │   ├── CrashGame.tsx
│   │   ├── CrashGame2.tsx
│   │   ├── CryptoKing.tsx
│   │   ├── HigherLowerGame.tsx
│   │   ├── Home.tsx
│   │   ├── Leaderboard.tsx
│   │   ├── Map.tsx
│   │   ├── PlinkoGame.tsx
│   │   ├── Rewards.tsx
│   │   ├── RollDiceGame.tsx
│   │   ├── Shop.tsx
│   │   ├── Squads.tsx
│   │   └── ...
│   ├── utils/
│   │   ├── validations/
│   │   │   └── customeToast.tsx
│   │   └── ...
│   ├── App.tsx
│   ├── main.tsx
│   ├── index.css
│   └── vite-env.d.ts
├── package.json
├── package-lock.json
├── vite.config.ts
├── tailwind.config.js
├── postcss.config.js
├── eslint.config.js
├── tsconfig.json
├── tsconfig.app.json
├── tsconfig.node.json
├── vercel.json
├── README.md
└── .gitignore
```

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```
2. Start the development server:
   ```
   npm run dev
   ```
3. Build for production:
   ```
   npm run build
   ```
4. Preview production build:
   ```
   npm run preview
   ```

## Features

- User authentication with Google integration
- Forgot password with OTP
- Interactive games:
  - Scratch Card
  - Wheel of Fortune
  - Plinko
  - Crash Game
  - Higher/Lower Game
  - Roll Dice Game
- Virtual currency system (Coins and FPP)
- Rewards and achievements system
- Real-time updates with Socket.io
- Responsive UI with Tailwind CSS
- Toast notifications
- Zendesk integration for customer support
- Map-based game navigation

## Technologies

- React 18
- TypeScript
- Vite
- Tailwind CSS
- React Router
- React Query (Tanstack Query)
- Socket.io
- Formik & Yup for form validation
- React Toastify for notifications
- Lucide React for icons
- Axios for API requests
- Day.js for date handling

## License

MIT
