import React, { useEffect, useState } from 'react';
import { X, CheckCircle } from 'lucide-react';
import useFetch from '../hooks/useFetch';
import { CONFIRM_PROFILE_UPDATE, GET_USER_PROFILE, POST_USER_INFO } from '../api/auth';
import { useAuth } from '../auth/AuthContext';
import { toast } from 'react-toastify';
import axios from 'axios';
interface ProfileSettingsProps {
  onClose: () => void;
}
interface ProfileData {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  phone: string;
  dateOfBirth: string;
}
const ProfileSettings: React.FC<ProfileSettingsProps> = ({ onClose }) => {
  const { accessToken } = useAuth();
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [otp, setOtp] = useState('');
  const [confirmationToken, setConfirmationToken] = useState('');
  const [profileData, setProfileData] = useState<ProfileData>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phone: '',
    dateOfBirth: '',
  });
  const [showSuccess, setShowSuccess] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  // Fetch user profile data
  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${accessToken}`, // Assuming you have accessToken in your context
    },
  });
  useEffect(() => {
    if (userProfile) {
      setProfileData({
        firstName: userProfile.first_name,
        lastName: userProfile.last_name,
        username: userProfile.username,
        email: userProfile.email,
        phone: userProfile.phone_number,
        dateOfBirth: '', // Set this from your profile data if available
      });
    }
  }, [userProfile]);
  const handleChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prepare data for posting
    const postData = {
      date_of_birth: profileData.dateOfBirth,
      email: profileData.email,
      first_name: profileData.firstName, // Use firstName
      last_name: profileData.lastName, // Use lastName
      phone: profileData.phone,
    };
    // Post the updated profile data
    const response = await fetch(POST_USER_INFO, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(postData),
    });
    if (response.ok) {
      // Handle successful response
      const result = await response.json();
      console.log("This is token from result", result?.token)
      setConfirmationToken(result?.token || ''); // expect backend to return this
      setShowOtpModal(true);
    } else {
      // Handle error response
      toast.error('Failed to update profile');
      // setShowSuccess(false);
    }
  };

const handleOtpConfirm = async () => {
    try {
      const { data: res } = await axios.post(
        CONFIRM_PROFILE_UPDATE,
        {
          otp,
          token: confirmationToken,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      // If the API responds with a success status
      setShowOtpModal(false);
      setShowSuccess(true);

      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 2000);
    } catch (error: any) {
      // If the server returns an error response, axios throws here
      const message =
        error.response?.data?.message || 'OTP confirmation failed';
      toast.error(message);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-lg">
        <div className="p-6 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white font-[Anton] tracking-wide">Edit Profile</h2>
          <button onClick={onClose} className="text-white/60 hover:text-white transition-colors">
            <X size={24} />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-white">First Name</label>
              <input
                type="text"
                value={profileData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Last Name</label>
              <input
                type="text"
                value={profileData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Username</label>
              <input
                type="text"
                value={profileData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Email</label>
              <input
                type="email"
                value={profileData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Phone Number</label>
              <input
                type="tel"
                value={profileData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
              />
            </div>
           <div>
  <label className="block text-sm font-medium mb-2 text-white">Date of Birth</label>
  <input
    type="date"
    value={profileData.dateOfBirth}
    onChange={(e) => handleChange('dateOfBirth', e.target.value)}
    max={new Date().toISOString().split('T')[0]} // Restrict future dates
    className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
  />
</div>

          </div>
          <div className="mt-6 flex gap-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!isDirty}
              className={`flex-1 py-3 rounded-lg font-medium transition-colors ${isDirty
                  ? 'bg-[#ED0CFF] text-white hover:bg-[#d30ae0]'
                  : 'bg-white/10 text-white/40 cursor-not-allowed'
                }`}
            >
              Save Changes
            </button>
          </div>
        </form>
        {/* Success Modal */}
        {showSuccess && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-white">Profile Updated!</h3>
              <p className="text-white/60">
                Your profile information has been successfully updated.
              </p>
            </div>
          </div>
        )}
      </div>
      {showOtpModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="relative bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 text-center w-full max-w-sm">
            <button
              onClick={() => setShowOtpModal(false)}
              className="absolute top-4 right-4 text-white/60 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
            <h3 className="text-xl font-bold mb-4 text-white">Enter OTP</h3>
            <input
              type="number"
              placeholder="Enter OTP"
              max={6}
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              className="w-full px-4 py-2 rounded-lg bg-[#1c1c1c] border border-[#510957] text-white mb-4 focus:outline-none"
            />
            <button
  onClick={handleOtpConfirm}
  disabled={otp.length < 4 || otp.length > 6}
  className={`w-full py-2 rounded-lg text-white font-semibold transition-colors ${
    otp.length >= 4 && otp.length <= 6
      ? 'bg-[#ED0CFF] hover:bg-[#d30ae0]'
      : 'bg-gray-500 cursor-not-allowed'
  }`}
>
  Confirm
</button>
          </div>
        </div>
      )}

    </div>

  );

};
export default ProfileSettings;