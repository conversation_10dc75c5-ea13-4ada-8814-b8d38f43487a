import React, { useState, useRef, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';

// Context and Auth
import { useAuth } from '../auth/AuthContext';
import { useSocketContext } from '../context/socketProvider';

// Hooks
import useFetch from '../hooks/useFetch';

// Components
import LoginPromptPopup from './LoginPromptPopup';
import Messages from './Messages';
import TermsAndConditions from './TermsAndConditions';
import PrivacyPolicy from './PrivacyPolicy';
import Settings from './Settings';
import HelpSupport from './HelpSupport';
import VideoAd from './VideoAd';
import BoostModal from './BoostModal';
import Profile from './Profile';
import Transactions from './Transactions';
import ZendeskWidget from './ZendeskWidget';

// Constants and API
import { IMAGES } from '../constant/image';
import { GET_USER_PROFILE } from '../api/auth';
import { markers } from '../constant/marker';

// Icons
import {
  BanknoteIcon,
  PlayCircle,
  Menu,
  Bell,
  LogOut,
  HelpCircle,
  Settings as SettingsIcon,
  Shield,
  FileText,
  Zap,
  User,
  Users,
  Map,
  Star,
  Receipt,
  X,
  Circle,
} from 'lucide-react';
import { getNotification } from '../api/notificationService';

// Type definitions
export interface Balance {
  coins: number;
  fpp: number;
  level: number;
  xp: number;
  real_money: string;
}

interface LayoutProps {
  balance: number | undefined;
}

interface LevelUpMarker {
  avatar: string;
  title: string;
  share_title: string;
  share_description: string;
}

// Utility functions
const formatCoins = (value: number | string): string => {
  // Round to nearest whole number and add commas
  const rounded = Math.round(Number(value));
  return rounded.toLocaleString();
};

const calculateLevelProgress = (current: number, required: number): number =>
  Math.min((current / required) * 100, 100);
const Layout: React.FC<LayoutProps> = ({ balance = 0 }) => {
  // Hooks
  const { isAuthenticated, logout, accessToken } = useAuth();
  const {
    levelPlayerState,
    notifyState,
    connectNotifySocket,
    connectLevelPlayerSocket,
  } = useSocketContext();
  const location = useLocation();
  const navigate = useNavigate();

  // Refs
  const prevLevelRef = useRef<number | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // UI State - Modals and Popups
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [showMessages, setShowMessages] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideoAd, setShowVideoAd] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showTransactions, setShowTransactions] = useState(false);
  const [showZendesk, setShowZendesk] = useState(false);

  // Notification Count
  const [count, setCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  // UI State - Menu and Level
  const [menuOpen, setMenuOpen] = useState(false);
  const [levelUpMarker, setLevelUpMarker] = useState<LevelUpMarker | null>(
    null
  );

  // Unused states - can be removed if not needed
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacy, setShowPrivacy] = useState(false);

  // API Data
  const {
    data: userProfile,
    loading,
    error,
  } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    skip: !isAuthenticated,
  });

  // Effect: Initialize socket connection on authentication
  useEffect(() => {
    if (isAuthenticated) {
      connectLevelPlayerSocket();
      connectNotifySocket();
    }
    setShowLevelUp(false);
  }, [isAuthenticated]);

  useEffect(() => {
    fetchNotificationsCount();
  }, [!showMessages]);

  const fetchNotificationsCount = async () => {
    if (!accessToken || isLoading) return;
    try {
      setIsLoading(true);
      const res = await getNotification({ page: 1, per_page: 10 }, accessToken);
      setCount(res?.unread_count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect: Handle level up detection
useEffect(() => {
  if (!isAuthenticated) return;

  const newLevel = levelPlayerState?.level;
  const prevLevel = prevLevelRef.current;

  if (newLevel !== undefined && prevLevel !== null && newLevel > prevLevel) {
    // Find marker that matches the new level
    const matchedMarker = markers.find(marker => marker.level === newLevel);

    if (matchedMarker) {
      setLevelUpMarker(matchedMarker);
      setShowLevelUp(true);
    }
  }

  if (newLevel != null) {
    prevLevelRef.current = newLevel;
  }
}, [levelPlayerState?.level, isAuthenticated]);


  // Effect: Setup UI interactions and Zendesk styling
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    // Initialize Zendesk
    setShowZendesk(true);

    // Add responsive styling for Zendesk widget
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      @media (max-width: 768px) {
        #launcher {
          bottom: 4.5rem !important;
          right: 1rem !important;
          z-index: 9999 !important;
        }
      }
    `;
    document.head.appendChild(styleTag);

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (document.head.contains(styleTag)) {
        document.head.removeChild(styleTag);
      }
    };
  }, []);

  // Event Handlers
  const handleShare = (platform: string) => {
    console.log(`Sharing to ${platform}`);
  };

  const handleNavClick = (e: React.MouseEvent, path: string) => {
    if (!isAuthenticated) {
      e.preventDefault();
      setShowLoginPopup(true);
      return;
    }
    navigate(path);
  };

  const handleBoostClick = (e: React.MouseEvent) => {
    if (!isAuthenticated) {
      e.preventDefault();
      setShowLoginPopup(true);
      return;
    }
    setShowBoostModal(true);
  };

  const handleLevelUpClose = () => {
    setShowLevelUp(false);
  };

  const handleLevelUpGoToMap = () => {
    setShowLevelUp(false);
    navigate('/map');
  };


console.log("levelMarker",levelUpMarker)

  return (
    <div className="min-h-screen bg-black text-text-primary">
      {/* Level Up Modal */}
      {showLevelUp && levelUpMarker && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
          <div className="bg-[#181818] rounded-2xl shadow-2xl p-6 max-w-xs w-full flex flex-col items-center relative">
            <button
              className="absolute top-3 right-3 text-white hover:text-primary transition-colors"
              onClick={handleLevelUpClose}
              aria-label="Close level up modal"
            >
              <X size={24} />
            </button>
            <img
              src={levelUpMarker.avatar}
              alt={levelUpMarker.title}
              className="w-20 h-20 rounded-full mb-4"
            />
            <h2 className="text-xl font-bold mb-2 text-center">
              {levelUpMarker.title}
            </h2>
            <p className="text-sm text-white/80 mb-2 text-center">
              {levelUpMarker.share_title}
            </p>
            <p className="text-xs text-white/60 mb-4 text-center">
              {levelUpMarker.share_description}
            </p>
            <button
              className="w-full bg-primary text-white py-2 rounded-xl font-bold mt-2 hover:bg-primary/90 transition-colors"
              onClick={handleLevelUpGoToMap}
            >
              Go to Map
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="sticky top-0 bg-black-card/80 bg-black backdrop-blur-sm p-4 flex md:justify-center gap-2  items-center z-50">
        {isAuthenticated ? (
          <div className="flex items-center space-x-2 sm:space-x-2 md:space-x-6">
            <p className="font-[haydes] font-bold text-sm sm:text-base md:text-lg">
              Hii {userProfile?.first_name}
            </p>

            <div className="ml-auto w-44 sm:w-40 md:w-52 bg-[#1c1c1c] rounded-md p-[12px]">
              <div className="flex flex-col">
                <div className="flex items-center gap-1">
                  <div className="flex items-center mr-2">
                    <Star size={16} className="text-accent-gold mr-1" />
                    <span className="text-xs text-white">
                      {levelPlayerState?.level || '0'}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2.5 overflow-hidden">
                    <div
                      className="bg-primary h-2.5 rounded-full transition-all duration-300"
                      style={{
                        width: `${calculateLevelProgress(
                          levelPlayerState?.amount_spent_to_reach_level ?? 0,
                          levelPlayerState?.next_level_requirement ?? 1
                        ).toFixed(2)}%`,
                      }}
                    />
                  </div>
                  <span className="text-xs text-white font-semibold ml-2 whitespace-nowrap cursor-pointer">
                    {formatCoins(
                      levelPlayerState?.amount_spent_to_reach_level ?? 0
                    )}{' '}
                    /{' '}
                    {formatCoins(levelPlayerState?.next_level_requirement ?? 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="max-w-md mx-auto flex justify-center  items-center">
            <div className="flex items-center md:gap-44 gap-40 w-full justify-between">
              <div>
                <img src={IMAGES.LOGO} alt="Logo" className="w-10 h-10 mr-2" />
                {/* <h1 className="text-xl font-bold text-white">Street Hustle</h1> */}
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={() => navigate('/auth?tab=signup')}
                  className="bg-primary text-white px-4 py-2 rounded-lg"
                >
                  Sign Up
                </button>
                <button
                  onClick={() => navigate('/auth?tab=login')}
                  className="border border-white text-white px-4 py-2 rounded-lg"
                >
                  Login
                </button>
              </div>
            </div>
          </div>
        )}

        {isAuthenticated && (
          <div
            className="flex items-center  md:gap-3 sm:px-[3px] relative"
            ref={menuRef}
          >
            <div
              onClick={(e) => handleNavClick(e, '/wallet')}
              className="ml-auto h-10 flex items-center gap-1.5 sm:gap-2 md:gap-3 px-4 sm:px-3 md:px-4 py-1.5 sm:py-2 rounded-lg cursor-pointer bg-[#1c1c1c]"
            >
              <BanknoteIcon
                size={16}
                className="text-accent-gold w-4 h-4 sm:w-5 sm:h-5"
              />
              <div className="truncate max-w-[80px]">
                <span className="text-xs font-normal text-white whitespace-nowrap overflow-hidden text-ellipsis block">
                  {formatCoins(balance)} Bucks
                </span>
              </div>
            </div>

            <button
              onClick={() => setShowMessages(true)}
              className="relative hover:text-primary transition-colors"
            >
              <Bell
                size={23}
                className="fill-current text-[#9c9c9c] stroke-none"
              />
              {count > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-[10px] font-bold rounded-full h-4 w-4 flex items-center justify-center">
                  {count > 9 ? '9+' : count}
                </span>
              )}
            </button>

            <button
              onClick={() => setMenuOpen(!menuOpen)}
              className="hover:text-primary transition-colors"
            >
              {!menuOpen ? <Menu size={24} /> : <X size={24} />}
            </button>

            {menuOpen && (
              <div className="absolute right-0 top-[3.5rem] w-64 bg-surface-card rounded-xl shadow-lg border border-text-muted/10 overflow-hidden z-50">
                <div className="p-4 border-b border-text-muted/10">
                  <div className="flex items-center gap-3">
                    <div>
                      <h3 className="font-semibold text-xs">
                        Player ID: {userProfile?.user_id}
                      </h3>
                      <p className="text-sm text-text-muted">
                        {userProfile?.email}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-2">
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowProfile(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <User size={18} className="text-primary" />
                    <span>Profile</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowTransactions(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <Receipt size={18} className="text-primary" />
                    <span>Transactions</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowSettings(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <SettingsIcon size={18} className="text-primary" />
                    <span>Settings</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowHelp(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <HelpCircle size={18} className="text-primary" />
                    <span>Help & Support</span>
                  </button>
                  <Link
                    to="/terms-and-conditions"
                    onClick={() => {
                      setMenuOpen(false);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <FileText size={18} className="text-primary" />
                    <span>Terms & Conditions</span>
                  </Link>
                  <Link
                    to="/privacy-policy"
                    onClick={() => {
                      setMenuOpen(false);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <Shield size={18} className="text-primary" />
                    <span>Privacy Policy</span>
                  </Link>
                  <div className="h-px bg-text-muted/10 my-2" />
                  <button
                    onClick={logout}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left text-error"
                  >
                    <LogOut size={18} />
                    <span>Sign Out</span>
                  </button>
                </div>
                <div className="p-3 text-center border-t border-text-muted/10">
                  <p className="text-xs text-text-muted">Version 1.0.0</p>
                </div>
              </div>
            )}
          </div>
        )}
      </header>

      {/* Main Content */}
      <main
        className={
          location.pathname === '/lottery' ||
          location.pathname === '/sportsbook'
            ? ''
            : 'p-4 max-w-md mx-auto pb-28'
        }
      >
        <Outlet />
      </main>

      {/* Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-[#131313E0] backdrop-blur-sm p-4 pt-0 border-t border-text-muted/10 z-30">
        <div className="max-w-md mx-auto flex justify-around">
          <Link
            to="/"
            className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4  transition-colors ${
              location.pathname === '/'
                ? 'text-[#ED0CFF]  border-t-[2px] border-[#ED0CFF]'
                : ''
            }`}
          >
            <PlayCircle size={24} />
            <span className="text-xs">Hustle</span>
          </Link>
          <Link
            to="/wallet"
            onClick={(e) => handleNavClick(e, '/wallet')}
            className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4  transition-colors ${
              location.pathname === '/wallet'
                ? 'text-[#ED0CFF]  border-t-[2px] border-[#ED0CFF]'
                : ''
            }`}
          >
            <BanknoteIcon size={24} />
            <span className="text-xs">Wallet</span>
          </Link>
          <Link
            to="/squads"
            onClick={(e) => handleNavClick(e, '/squads')}
            className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4  transition-colors ${
              location.pathname === '/squads'
                ? 'text-[#ED0CFF]  border-t-[2px] border-[#ED0CFF]'
                : ''
            }`}
          >
            <Users size={24} />
            <span className="text-xs">Squads</span>
          </Link>
          <Link
            to="/map"
            onClick={(e) => handleNavClick(e, '/map')}
            className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4  transition-colors ${
              location.pathname === '/map'
                ? 'text-[#ED0CFF]  border-t-[2px] border-[#ED0CFF]'
                : ''
            }`}
          >
            <Map size={24} />
            <span className="text-xs">Map</span>
          </Link>
          <Link
            to="/lottery"
            onClick={(e) => handleNavClick(e, '/lottery')}
            className={`flex flex-col items-center gap-1 pt-4 transition-colors hover:text-[#ED0CFF] ${
              location.pathname === '/lottery'
                ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                : 'text-white'
            }`}
          >
            {/* SVG icon for Lottery */}
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="hover:text-[#ED0CFF]"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="2"
                fill="#1c1c1c"
              />
              <path
                d="M8 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0z"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
              />
              <circle cx="12" cy="12" r="2" fill="currentColor" />
            </svg>
            <span className="text-xs">Lottery</span>
          </Link>

          <Link
            to="/sportsbook"
            onClick={(e) => handleNavClick(e, '/sportsbook')}
            className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors ${
              location.pathname === '/sportsbook'
                ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                : 'text-white'
            }`}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="hover:text-[#ED0CFF]"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="1"
                fill="none"
              />
              <path
                d="M12 2L15.5 8.5L22 9L17 14L18.5 20.5L12 17L5.5 20.5L7 14L2 9L8.5 8.5L12 2Z"
                fill="currentColor"
              />
              <path
                d="M12 4L14 7.5L17 8L15 10.5L15.5 13.5L12 12L8.5 13.5L9 10.5L7 8L10 7.5L12 4Z"
                fill={
                  location.pathname === '/sportsbook' ? '#ED0CFF' : '#000000'
                }
              />
              <path
                d="M12 6L13 8L15 8.5L13.5 10L14 11.5L12 10.5L10 11.5L10.5 10L9 8.5L11 8L12 6Z"
                fill="currentColor"
              />
            </svg>
            <span className="text-xs">Sports</span>
          </Link>

          <button
            onClick={handleBoostClick}
            className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4  transition-colors"
          >
            <Zap size={24} />
            <span className="text-xs">Boost</span>
          </button>
        </div>

        {/* ZendeskWidget */}
        {showZendesk && (
          <ZendeskWidget userProfileState={{ userProfile, loading, error }} />
        )}
      </nav>

      {/* Login Prompt Popup */}
      {showLoginPopup && (
        <LoginPromptPopup
          isOpen={showLoginPopup}
          onClose={() => setShowLoginPopup(false)}
        />
      )}

      {/* Modals */}
      {showMessages && <Messages onClose={() => setShowMessages(false)} />}
      {showTerms && <TermsAndConditions />}
      {showPrivacy && <PrivacyPolicy />}
      {showSettings && <Settings onClose={() => setShowSettings(false)} />}
      {showHelp && <HelpSupport onClose={() => setShowHelp(false)} />}
      {showProfile && <Profile onClose={() => setShowProfile(false)} />}
      {showTransactions && (
        <Transactions onClose={() => setShowTransactions(false)} />
      )}
      {showVideoAd && (
        <VideoAd onClose={() => setShowVideoAd(false)} onComplete={() => {}} />
      )}
      {showBoostModal && (
        <BoostModal
          onClose={() => setShowBoostModal(false)}
          onWatchAd={() => {
            setShowBoostModal(false);
            setShowVideoAd(true);
          }}
          onShare={handleShare}
        />
      )}
    </div>
  );
};

export default Layout;
