import React, { useState, useEffect } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

const STORY_SLIDES = [
  {
    title: "Welcome to Playzuzu",
    description: "Your journey from street hustler to Lagos legend begins here. Every champion starts somewhere - your story starts now.",
    image: "https://cdn.midjourney.com/3bf4b272-2500-4639-8d6b-4ac412fa441d/0_0.png"
  },
  {
    title: "Master the Streets",
    description: "Start small, think big. Learn the hustle, play smart, and build your empire one game at a time. In Lagos, opportunity knocks for those who dare to answer.",
    image: "https://cdn.midjourney.com/3bf4b272-2500-4639-8d6b-4ac412fa441d/0_1.png"
  },
  {
    title: "Rise Through the Ranks",
    description: "From street corners to high-stakes games, your reputation grows with every win. Climb the ladder, earn respect, and become a true Lagos high-roller.",
    image: "https://cdn.midjourney.com/49b45592-1b81-4c6e-8d83-b7854596111d/0_1.png"
  },
  {
    title: "Build Your Empire",
    description: "Turn your winnings into wealth. Invest wisely, expand your influence, and transform from hustler to business mogul. The streets made you, success defines you.",
    image: "https://cdn.midjourney.com/3bf4b272-2500-4639-8d6b-4ac412fa441d/0_2.png"
  }
];

interface StoryIntroProps {
  onClose: () => void;
}

const StoryIntro: React.FC<StoryIntroProps> = ({ onClose }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false });
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShowModal(true), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    emblaApi.on('select', () => {
      setCurrentSlide(emblaApi.selectedScrollSnap());
    });
  }, [emblaApi]);

  const scrollPrev = () => {
    if (emblaApi) emblaApi.scrollPrev();
  };

  const scrollNext = () => {
    if (emblaApi) emblaApi.scrollNext();
  };

  const handleClose = () => {
    window.localStorage.setItem('hasSeenStoryIntro', 'true');
    setShowModal(false);
    setTimeout(onClose, 300); // Wait for fade out animation
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-opacity duration-300 ${showModal ? 'opacity-100' : 'opacity-0'
      }`}>
      <div className="absolute inset-0 bg-black/90 backdrop-blur-sm" onClick={handleClose} />

      <div className="relative w-full max-w-4xl bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl overflow-hidden">
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-10 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white hover:bg-black/70 transition-colors"
        >
          <X size={24} />
        </button>

        {/* Carousel */}
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {STORY_SLIDES.map((slide, index) => (
              <div key={index} className="relative flex-[0_0_100%] min-w-0">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src={slide.image}
                    alt={slide.title}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-8">
                    <h2 className="text-4xl font-bold mb-4 text-white">{slide.title}</h2>
                    <p className="text-xl text-white/90 max-w-2xl leading-relaxed">{slide.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="absolute bottom-8 left-0 right-0 flex justify-between items-center px-8">
          <button
            onClick={scrollPrev}
            disabled={currentSlide === 0}
            className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center text-white hover:bg-white/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft size={24} />
          </button>

          {/* Progress indicators */}
          <div className="flex gap-2">
            {STORY_SLIDES.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? 'bg-yellow-400' : 'bg-white/30'
                  }`}
              />
            ))}
          </div>

          {currentSlide === STORY_SLIDES.length - 1 ? (
            <button
              onClick={handleClose}
              className="px-6 py-2 rounded-full bg-yellow-500 text-black font-medium hover:bg-yellow-400 transition-colors"
            >
              Get Started
            </button>
          ) : (
            <button
              onClick={scrollNext}
              className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center text-white hover:bg-white/20 transition-colors"
            >
              <ChevronRight size={24} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default StoryIntro;