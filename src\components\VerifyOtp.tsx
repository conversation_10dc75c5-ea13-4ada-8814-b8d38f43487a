import React from 'react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-toastify';
import { ChevronLeft } from 'lucide-react';
import { verifyOtp } from '../api/authService';
import { CustomToast } from '../utils/validations/customeToast';

interface VerifyOtpProps {
  username: string;
  onSwitchTab: (token?: string) => void;
  handleVerifyOtp: (token: string) => void
}

interface FormValues {
  otp: string;
}

const VerifyOtpSchema = Yup.object().shape({
  otp: Yup.string()
    .matches(/^\d{6}$/, 'OTP must be exactly 6 digits')
    .required('OTP is required'),
});

const VerifyOtp: React.FC<VerifyOtpProps> = ({ username, onSwitchTab, handleVerifyOtp }) => {
  const initialValues: FormValues = { otp: '' };

  const handleSubmit = async (
    values: FormValues,
    { setSubmitting, setStatus }: FormikHelpers<FormValues>
  ) => {
    setStatus(undefined);
    try {
      const response = await verifyOtp({ username, otp: values.otp });
      if (!response.token) {
        setStatus('Unexpected response from server.');
        toast.error('Unexpected response from server.');
        return;
      }
      toast.success('OTP verified successfully!');
      handleVerifyOtp(response.token);
    } catch (err: any) {
      const message =
        err?.response?.data?.message || err?.message || 'Something went wrong';
      toast.error(message);
      setStatus(message); // optional: show below form field
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-sm mx-auto">
      <div className="flex items-center gap-1.3 mb-4">
        <button
          type="button"
          onClick={() => {
            onSwitchTab();
          }}
          className="text-white"
        >
          <ChevronLeft className='font-bold' />
        </button>
        <h2 className="text-[16px] leading-[24px] font-bold tracking-[0.02rem] uppercase font-[Anton] text-white">
          VERIFY OTP
        </h2>

      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={VerifyOtpSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, status }) => (
          <Form >
            {/* Header */}

            {/* Server/Form-level error */}
            {status && (
              <div className="text-red-500 text-sm mb-4">{status}</div>
            )}

            {/* OTP Field */}
            <div className="mb-4">
              <label
                htmlFor="otp"
                className="block text-sm font-[400] font-[Poppins] text-white mb-1"
              >
                OTP
              </label>
              <Field
                id="otp"
                name="otp"
                type="text"
                placeholder="Ex. 875939"
                maxLength={6}
                className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
              />
              <ErrorMessage
                name="otp"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>

            {/* Submit */}
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-[#ED0CFF] hover:bg-[#d30ae0] transition   tracking-wider
                     font-bold uppercase font-[Anton] duration-200 text-white font-normal py-2 rounded-lg"
            >
              {isSubmitting ? 'Verifying…' : 'Verify OTP'}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VerifyOtp;
