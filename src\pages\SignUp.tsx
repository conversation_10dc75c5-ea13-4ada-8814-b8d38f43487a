import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, Phone, User, Lock, ChevronLeft, Eye, EyeOff } from 'lucide-react';

const SignUp = () => {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    phone_number: '',
    email: '',
    password: ''
  });
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.phone_number || !formData.email || !formData.password) {
      setError('All fields are required');
      return;
    }

    if (formData.username === 'test' && formData.password === 'test') {
      navigate('/');
    } else {
      setError('Invalid credentials. Use test/test to login.');
    }
  };

  return (
    <div className="min-h-screen relative flex flex-col items-center justify-center py-8">
      {/* Background Image */}
      <div className="absolute inset-0 -z-10">
        <img 
          src="https://cdn.midjourney.com/3bf4b272-2500-4639-8d6b-4ac412fa441d/0_0.png"
          alt="Background"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/70" />
      </div>

      {/* Back Button */}
      <button
        onClick={() => navigate('/')}
        className="absolute top-4 left-4 text-white/80 hover:text-white flex items-center gap-2"
      >
        <ChevronLeft size={20} />
        <span>Back</span>
      </button>

      <div className="w-full max-w-md px-4">
        {/* Title */}
        <div className="text-center mb-6">
          <h1 className="text-4xl font-bold text-white mb-2">Join Playzuzu</h1>
          <p className="text-lg text-white/80">Start your journey from streets to success</p>
        </div>

        {/* Signup Form */}
        <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 shadow-xl backdrop-blur-sm bg-opacity-50">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* username */}
            <div>
              <label className="block text-sm font-medium mb-1">username</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                  placeholder="Enter username"
                />
                <User className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />
              </div>
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium mb-1">Phone Number</label>
              <div className="relative">
                <input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => setFormData({ ...formData, phone_number: e.target.value })}
                  className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                  placeholder="Enter phone number"
                />
                <Phone className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />
              </div>
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <div className="relative">
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                  placeholder="Enter email address"
                />
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />
              </div>
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-10 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                  placeholder="Enter password"
                />
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-white/40 hover:text-white/60"
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-2 text-sm text-red-400">
                {error}
              </div>
            )}

            <button
              type="submit"
              className="w-full bg-yellow-500 text-black font-medium py-2 rounded-lg hover:bg-yellow-400 transition-colors"
            >
              Create Account
            </button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-white/10"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gradient-to-b from-purple-900 to-indigo-900 text-white/60">
                  Or continue with
                </span>
              </div>
            </div>

            <button
              type="button"
              className="w-full bg-white/10 border border-white/20 text-white font-medium py-2 rounded-lg hover:bg-white/20 transition-colors flex items-center justify-center gap-2"
            >
              <img 
                src="https://www.google.com/favicon.ico" 
                alt="Google" 
                className="w-5 h-5"
              />
              Sign up with Google
            </button>

            <p className="text-center text-sm text-white/60">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => navigate('/login')}
                className="text-yellow-400 hover:text-yellow-300"
              >
                Sign in
              </button>
            </p>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SignUp;