import React, { useEffect, useRef, useState } from 'react';
import mapImageSrc from '../assets/images/map.avif';
import { IMAGES } from '../constant/image';
import { X } from 'lucide-react';
import { useSocketContext } from '../context/socketProvider';
import { Marker,markers } from '../constant/marker';


interface HoverModalProps {
  marker: Marker;
  onClose: () => void;
  setRewardVisible: (visible: boolean) => void;
  setImage: (image: string) => void;
  setBackGroundColor: (backGroundColor: string) => void;
  setBottomData: (bottomData: {}) => void
}

const HoverModal: React.FC<HoverModalProps> = ({ marker, onClose, setRewardVisible, setImage, setBackGroundColor, setBottomData }) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
    <div
      className="relative w-full max-w-[220px] h-[320px] rounded-[24px] overflow-hidden shadow-2xl flex flex-col justify-between"
      style={{
        backgroundImage: `url(${marker.bg_image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        height: 270
      }}
    >
      {/* Overlay */}
      <div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          backgroundImage: `linear-gradient(360deg, ${marker?.bg_color}, #000000a3)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />



      {/* Top controls */}
      <div className="absolute top-3 left-3 z-10">
        <img src={IMAGES.LOCK} alt="Lock Icon" className="w-6 h-6" />
      </div>
      <button
        onClick={onClose}
        className="absolute top-3 right-3 text-white hover:text-gray-300 transition z-30"
        aria-label="Close"
      >
        <X size={24} />
      </button>

      {/* Main content */}
      <div className="relative z-10 flex flex-col justify-center items-center px-6 pt-24 pb-6 text-white ">
        <h2 className="text-[20px] font-bold mb-2 font-[Anton] tracking-wide">{marker.title}</h2>
        <p className="text-[12px] mb-6 font-sans font-normal text-center">
          Earn <span className="font-bold ">x Bucks</span> & <span className="font-bold">x Rise</span> when unlocked
        </p>
        <button

          style={{
            backgroundColor: marker?.bg_color,
          }}
          className={`w-full  transition text-white  py-2 rounded-xl text-[14px] shadow-md font-[Anton] tracking-wide`}      >
          {marker.price} UNLOCK
        </button>

      </div>
    </div>
  </div>
);


const RewardModal: React.FC<{ bottomData: Marker, backGroundColor: string, image: string, onClose: () => void }> = ({ onClose, backGroundColor, bottomData, image }) => (
  <div className="fixed inset-0 z-50 flex items-end md:items-center justify-center bg-black/60 backdrop-blur-sm md:p-8 transition duration-300 ease-out">
    <div
      style={{
        backgroundImage: `url(${image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',

      }}
      className="relative w-full max-w-md md:max-w-sm  h-[640px] md:h-[500px] rounded-t-2xl md:rounded-2xl overflow-hidden text-white shadow-2xl"
    >
      {/* Overlay */}
      {/* <div className="absolute inset-0  bg-[linear-gradient(360deg,#9509a0,#000000a3)]" /> */}
      <div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          backgroundImage: `linear-gradient(360deg, ${backGroundColor}, #000000a3)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />

      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-3 right-3 z-30 text-white hover:text-gray-300 cursor-pointer"
        aria-label="Close"
      >
        <X size={24} />
      </button>

      {/* Modal Content */}
      <div className="absolute bottom-20 md:bottom-0  w-full z-20 flex flex-col items-center  text-center px-6  md:py-12">
        <div className="flex flex-col items-center w-[252px] mb-8 md:mb-0 justify-end">
          <h2 className="text-[32px] font-[Anton] mb-3  tracking-wider">
            {bottomData?.title}
          </h2>

          <p className="text-[16px] text-white/80 mb-2 tracking-wider">
            {bottomData?.share_title}
          </p>

          <p className="text-[16px]  font-medium mt-8 md:mt-0 mb-0 md:mb-6 tracking-wider">
            {bottomData?.share_description}
          </p>
        </div>

        <button

          className="w-full bg-white  transition text-black font-bold py-2  rounded-xl text-lg font-[Anton] tracking-wide shadow-lg">
          SHARE
        </button>
      </div>

    </div>
  </div>
);



interface GameMapHTMLProps {
  currentLevel?: number;
}

const GameMapHTML: React.FC<GameMapHTMLProps> = ({ currentLevel = 0 }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [rewardVisible, setRewardVisible] = useState(false);
  const [image, setImage] = useState<string | null>(null)
  const [backGroundColor, setBackGroundColor] = useState<string | null>(null)
  const [bottomData, setBottomData] = useState<Record<string, any>>({})
  const {
    levelPlayerState,
    connectLevelPlayerSocket,
  } = useSocketContext();
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        setSize({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight,
        });
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const naturalWidth = 1920; // actual map image width
  const naturalHeight = 1080; // actual map image height
  const scale = Math.min(size.width / naturalWidth, size.height / naturalHeight);
  const avatarSize = Math.max(30, Math.min(150, 100 * scale));


  return (
    <div ref={containerRef} className="relative w-full h-[80vh] bg-black">
      <img
        src={mapImageSrc}
        alt="Game Map"
        className="w-full h-full object-cover"
      />

      {markers.map((marker, idx) => {
        const left = marker.x * scale;
        const top = marker.y * scale;

        const isColored = marker?.level <= levelPlayerState?.level;
        // const isClickable = idx <= levelPlayerState?.level;
        return (
          <button
            key={idx}
            onClick={() => {
              if (!marker.clickable) return;

              if (isColored) {
                // Unlocked: show reward
                setBottomData(marker);
                setImage(marker.bg_image);
                setBackGroundColor(marker.bg_color);
                setRewardVisible(true);
              } else {
                // Locked: show hover/info modal
                setOpenIndex(idx);
              }
            }}
            style={{ left, top }}
            className="
    absolute transform -translate-x-1/2 -translate-y-1/2
    flex flex-col items-center space-y-1 z-20
  "
          >
            {/* Avatar Button */}
            <div
              className="
      relative rounded-full border-2 border-white/20
      overflow-hidden shadow-md bg-white/20
    "
              style={{ width: avatarSize, height: avatarSize }}
            >
              <img
                src={marker.avatar}
                alt={marker.title}
                className={`w-full h-full object-cover rounded-full ${!isColored ? 'grayscale' : ''}`}
              />

              {/* Lock icon if marker is not yet unlocked */}
              {idx > currentLevel && (
                <img
                  src={IMAGES.LOCK}
                  alt="Locked"
                  className="absolute top-1 right-1 w-6 h-6 z-10 drop-shadow-md pointer-events-none"
                />
              )}
            </div>

            {/* Level Badge */}
            <span className="text-xs font-semibold text-white bg-black bg-opacity-60 px-2 py-0.5 rounded-full shadow">
              Lv. {marker.level || idx + 1}
            </span>
          </button>


        );
      })}




      {openIndex !== null && (
        <HoverModal
          marker={markers[openIndex]}
          onClose={() => setOpenIndex(null)}
          setRewardVisible={setRewardVisible}
          setImage={setImage}
          setBackGroundColor={setBackGroundColor}
          setBottomData={setBottomData}
        />
      )}
      {rewardVisible && (
        <RewardModal onClose={() => setRewardVisible(false)} image={image ?? ''} backGroundColor={backGroundColor ?? ''} bottomData={bottomData as Marker} />
      )}

    </div>
  );
};

export default GameMapHTML;
