import axios from 'axios';
import {
  PLINKO_DROP,
  GET_PLNKO_GAME_CONFIG,
  GET_PLNKO_GAME_HISTORY
} from './auth';


export interface HistoryData {
    page: number;
    per_page: number;
  }

export interface SelectQueryParams {
    id: string,
    user_guess: string,
    user_id: string
  }

export const plinkoDrop = async (
  data: {amount:number,currency:string},
  token: string
): Promise<any> => {
  const response = await axios.post(PLINKO_DROP, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

export const getPlinkoConfig = async (): Promise<any> => {
    const response = await axios.get(GET_PLNKO_GAME_CONFIG);
    return response.data;
  };

export const getPlinkoGameHistory = async (data: HistoryData, token: string) => {
    const response = await axios.get(GET_PLNKO_GAME_HISTORY, {
      headers: {
        Authorization: `<PERSON><PERSON> ${token}`,
      },
      params: {
        page: data.page,
        perPage: data.per_page,
      },
    });
  
    return response.data;
  };