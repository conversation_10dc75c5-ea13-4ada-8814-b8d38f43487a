import React, { useState, useEffect } from 'react';
import { Gift } from 'lucide-react';
import winSound from '../Sounds/WheelofFortune/WoFWin.mov';
import BackGroundSound from '../Sounds/WheelofFortune/Wheen_of_Fortune_Spin.mp3';
import SoundManager from '../components/soundManager/SoundManager';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  fetchSpinningWheelConfig,
  getSpinningWheelConfigs,
  getWheelPrice,
  SpinningWheelResponse,
} from '../api/wheelSpeenService';
import { useAuth } from '../auth/AuthContext';
import { PzPopup } from './Shared/PzPopup';
import { PzButton } from './Shared/PzButton';
import { IMAGES } from '../constant/image';
import { preloadImage } from '../utils/helper';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from './GameLoadingScreen/GameLoadingScreen';
import { CustomToast } from '../utils/validations/customeToast';
import { PzErrorPopup } from './Shared/PzErrorPopup';
interface WheelOfFortuneProps {
  onClose: () => void;
  onWin: (reward: {
    type: 'coins' | 'fpp' | 'xp' | 'mystery_box' | 'airtime' | 'merch';
    amount: number;
  }) => void;
  onPurchase: (coast: number) => void;
  balance: number | null;
}

const segmentColors = {
  blue: {
    color1: '#0CC6FF',
    color2: '#005CB8',
  },
  black: { color1: '#282828', color2: '#000000' },
  yellow: {
    color1: '#FFCE0C',
    color2: '#B89300',
  },
  violet: {
    color1: '#ED0CFF',
    color2: '#9400A0',
  },
  white: {
    color1: '#FFFFFF',
    color2: '#A8A8A8',
  },
};

const ANIMATION_TIME = 6000;

const WheelOfFortune: React.FC<WheelOfFortuneProps> = ({
  onClose,
  onWin,
  onPurchase,
  balance,
}) => {
  const [isSpinning, setIsSpinning] = useState<boolean>(false);
  const { accessToken } = useAuth();
  const [rotation, setRotation] = useState(0);
  const [showReward, setShowReward] = useState<boolean>(false);
  const [winData, setWinData] = useState<
    (SpinningWheelResponse & { winningIndex?: number }) | null
  >(null);
  const [didWin, setDidWin] = useState(false);
  const [showPurchaseError, setShowPurchaseError] = useState(false);
  const [cost, setCost] = useState<number>(0);
  const [targetIndex, setTargetIndex] = useState<number>(0);
  const [transition, setTransition] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  const { data: wheelConfig, isError: isConfigError } = useQuery({
    queryKey: ['wheelConfig'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getSpinningWheelConfigs(1, 20, String(accessToken));
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  const rewardsNumber = Number(wheelConfig?.length);

  const {
    data: wheelPrice,
    isError: isPriceError,
    refetch: refetchPrice,
  } = useQuery({
    queryKey: ['wheelPrice'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getWheelPrice(accessToken);
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  const placeBetMutation = useMutation<SpinningWheelResponse>({
    mutationFn: async () => {
      if (!accessToken) throw new Error('Access token missing');
      const data = await fetchSpinningWheelConfig(accessToken);
      setWinData(data);
      onPurchase(Number(data?.data.bet_amount));
      return data;
    },
  });

  const { isGameDataLoading, visibility } = useGameDataLoading(
    Number.isFinite(balance),
    Boolean(rewardsNumber),
    Boolean(wheelPrice)
  );

  useEffect(() => {
    // Preload images to optimize loading
    wheelConfig?.forEach((wheelItem) => preloadImage(wheelItem.icon));
  }, [wheelConfig]);

  useEffect(() => {
    wheelPrice?.price && setCost(wheelPrice.price);
  }, [wheelPrice]);

  useEffect(() => {
    isPriceError && CustomToast('error', 'Failed to get price');
    isConfigError && CustomToast('error', 'Failed to get configuration');
  }, [isPriceError, isConfigError]);

  const rotateToIndex = (targetIndex = 0) => {
    const segmentSize = rewardsNumber ? 360 / rewardsNumber : 0;
    const targetRotation = 360 - (rotation % 360) - targetIndex * segmentSize;
    const winningAngleShift = 180 / rewardsNumber;

    const totalRotation = 360 - 90 - winningAngleShift + targetRotation;

    setRotation((prev) => prev + totalRotation);
    setTransition('transform 6s linear');
  };

  const spinWheel = () => {
    if (cost > Number(balance)) return setShowPurchaseError(true);
    if (isSpinning) return;
    setIsSpinning(true);
    setIsProcessing(true);
    setDidWin(false);

    // Start rotation before placeBetMutation to avoid the pause break
    setTransition('transform 3s linear');
    setRotation((prev) => prev + 360);
    // Refetch price as it may be changed from PAM
    refetchPrice();
    // 1️⃣ Call bet API
    placeBetMutation.mutate(undefined, {
      onSuccess: (result) => {
        const winningIndex =
          wheelConfig?.findIndex((segment) => result.data.id === segment?.id) ||
          0;

        setTargetIndex(winningIndex);
        setWinData({ ...result, winningIndex });

        setTimeout(() => setShowReward(false), ANIMATION_TIME * 2);

        rotateToIndex(winningIndex);

        setTimeout(() => {
          setShowReward(true);
          setDidWin(true);
          setIsProcessing(false);

          onWin?.({
            type: 'coins',
            amount: Number(result.data?.prize?.match(/\d+/)?.[0]),
          });
        }, ANIMATION_TIME + 1000);
        setTimeout(() => {
          setIsSpinning(false);
        }, ANIMATION_TIME);
      },
      onError: () => CustomToast('error', 'Failed to place bet.'),
    });
  };

  return (
    <>
      {isGameDataLoading && <GameLoadingScreen />}

      <div
        style={{
          visibility: visibility ? visibility : 'hidden',
        }}
      >
        <SoundManager
          sounds={{
            background: BackGroundSound,
            win: winSound,
          }}
          // loop the background while the game is running:
          backgroundKey={isSpinning ? 'background' : null}
          // play one of these exactly once when it changes:
          playKey={didWin ? 'win' : undefined}
          volumes={{
            background: 0.4,
            win: 1.0,
          }}
        />
        {showReward ? (
          <PzPopup onClose={() => setShowReward(false)}>
            <div className="text-center">
              <div className="min-h-24">
                <img
                  src={wheelConfig?.[targetIndex]?.icon || IMAGES.DOLLAR}
                  alt="Win image"
                  className="m-auto max-h-24"
                />
              </div>
              <h3
                className="text-white text-4xl mt-4 mb-1"
                style={{
                  fontFamily: 'Bebas Neue',
                }}
              >
                CONGRATULATIONS!
              </h3>
              <p className="text-white font-[Anton] text-sm mb-8">
                You won {wheelConfig?.[targetIndex].amount} bucks
              </p>
              <PzButton text="Take it!" onClick={() => setShowReward(false)} />
            </div>
          </PzPopup>
        ) : (
          <PzPopup title="Wheel of Luck" onClose={onClose}>
            <div className="relative">
              <div className="relative aspect-square mb-8 mt-8">
                <svg
                  viewBox="-25 -25 450 450"
                  className="w-full filter drop-shadow-xl"
                >
                  <circle
                    cx="200"
                    cy="200"
                    r="207"
                    fill="none"
                    stroke="white"
                    strokeWidth="10"
                  />
                  <circle
                    cx="200"
                    cy="200"
                    r="193"
                    fill="none"
                    stroke="black"
                    strokeWidth="25"
                  />

                  {new Array(wheelConfig?.length)
                    .fill(IMAGES.LIGHT)
                    .map((image, index) => {
                      const baseAngle = 190;
                      const imageShift = 10;
                      const angleShift = baseAngle + 2;
                      const angle = (index * 360) / 12;
                      const rad = (angle * Math.PI) / 180;
                      const x = 190 + Math.cos(rad) * angleShift - imageShift;
                      const y = 190 + Math.sin(rad) * angleShift - imageShift;
                      return (
                        <image
                          key={index}
                          href={image}
                          width="40"
                          x={x}
                          y={y}
                        />
                      );
                    })}

                  <g
                    style={{
                      willChange: 'transform',
                      transform: `rotate(${rotation}deg)`,
                      transformOrigin: '200px 200px',
                      ...(isSpinning && { transition }),
                    }}
                  >
                    {wheelConfig?.map((reward, index) => {
                      const centerCoord = 200;
                      const halfCircle = 180;
                      const segmentRibShift = 1;
                      const textShift = 360 / wheelConfig.length / 2;

                      const angle = (index * 360) / rewardsNumber;
                      const nextAngle = ((index + 1) * 360) / rewardsNumber;
                      const midAngle = (angle + nextAngle) / 2;
                      const radMid = (midAngle * Math.PI) / halfCircle;
                      const ribAngle = angle - segmentRibShift;

                      const textRadius = 120;
                      const textX = centerCoord + Math.cos(radMid) * textRadius;
                      const textY = centerCoord + Math.sin(radMid) * textRadius;

                      const { color1, color2 } =
                        segmentColors[reward.color || 'blue'];

                      return (
                        <g key={index}>
                          <defs>
                            <linearGradient
                              id={`segment-gradient-${index}`}
                              x1="0%"
                              y1="0%"
                              x2="100%"
                              y2="100%"
                            >
                              <stop
                                offset="0%"
                                stopColor={color1}
                                stopOpacity="1"
                              />
                              <stop
                                offset="100%"
                                stopColor={color2}
                                stopOpacity="0.7"
                              />
                            </linearGradient>
                          </defs>
                          <path
                            d={`M ${centerCoord} ${centerCoord}
                          L ${
                            centerCoord +
                            halfCircle *
                              Math.cos((angle * Math.PI) / halfCircle)
                          } ${
                              centerCoord +
                              halfCircle *
                                Math.sin((angle * Math.PI) / halfCircle)
                            }
                          A ${halfCircle} ${halfCircle} 0 0 1 ${
                              centerCoord +
                              halfCircle *
                                Math.cos((nextAngle * Math.PI) / halfCircle)
                            } ${
                              centerCoord +
                              halfCircle *
                                Math.sin((nextAngle * Math.PI) / halfCircle)
                            }
                          Z`}
                            fill={`url(#segment-gradient-${index})`}
                          />
                          <g
                            transform={`translate(${textX} ${textY}) rotate(${
                              angle + textShift
                            }) scale(${
                              angle > 60 && angle < 270 ? '-1, -1' : '1, 1'
                            })`}
                          >
                            <text
                              fill="white"
                              fontSize="20"
                              fontFamily="Anton, Sans-serif"
                              fontWeight="bold"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="outlined-text"
                            >
                              {reward.name}
                            </text>
                          </g>
                          <image
                            href={IMAGES.SEGMENT_BORDER}
                            width={halfCircle}
                            x={centerCoord}
                            y={centerCoord}
                            transform={`rotate(${ribAngle} ${centerCoord} ${centerCoord})`}
                          />
                        </g>
                      );
                    })}
                  </g>
                  <image
                    href={IMAGES.WHEEL_SPEAR}
                    width="500"
                    x="-47"
                    y="-50"
                  />
                </svg>

                <div
                  style={{
                    width: '80%',
                    height: '80%',
                    borderRadius: '50%',
                    boxShadow: 'rgb(0, 0, 0) 0px 0px 20px 3px inset',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                  }}
                />

                {showReward && wheelConfig && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm rounded-xl animate-fade-in">
                    <div className="text-center">
                      {winData?.data?.prize?.toLowerCase() !== 'point 0' ? (
                        <>
                          <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                            {winData?.data?.prize?.toLowerCase() ===
                            'points' ? (
                              <Gift size={40} className="text-white" />
                            ) : (
                              <span className="text-4xl">🎪</span>
                            )}
                          </div>
                          <p className="text-3xl font-bold text-white mb-2">
                            {winData?.data?.prize?.toLowerCase() === 'points'
                              ? 'Mystery Box!'
                              : 'Congratulations! 🎉'}
                          </p>
                          <p className="text-xl text-white">
                            {winData?.data?.prize}
                          </p>
                        </>
                      ) : (
                        <>
                          <div className="w-20 h-20 bg-gray-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span className="text-4xl">🎯</span>
                          </div>
                          <p className="text-2xl font-bold text-white mb-2">
                            Better luck next time!
                          </p>
                          <p className="text-lg text-white/60">
                            Keep spinning for a chance to win!
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <PzButton
                text={isProcessing ? 'Spinning...' : 'Spin the Wheel!'}
                isDisabled={isProcessing}
                onClick={spinWheel}
              />
              <div className="text-sm m-auto text-center mt-2 font-[Anton]">
                Spin the wheel for {cost} bucks
              </div>
            </div>
          </PzPopup>
        )}
        {showPurchaseError && (
          <PzErrorPopup
            setShowFundsErrorPopup={setShowPurchaseError}
            cost={cost}
          />
        )}
      </div>
    </>
  );
};

export default WheelOfFortune;
