import { FC, ReactNode } from 'react';
import { IMAGES } from '../../constant/image';
import { X } from 'lucide-react';

export const PzPopup: FC<{
  children: ReactNode;
  title?: string;
  onClose?: () => void;
}> = ({ children, title, onClose }) => (
  <div className="fixed z-50 bg-black/70 bottom-0 left-0 right-0 h-full touch-none sm:inset-0 sm:flex sm:items-center sm:justify-center">
    <div
      className="bg-black rounded-2xl p-6 touch-none max-sm:pb-8 max-sm:absolute max-sm:rounded-b-none max-sm:bottom-0 max-sm:left-0 max-sm:right-0 sm:w-full sm:max-w-sm"
      style={{
        backgroundImage: `url(${IMAGES.ECLIPSE_BG})`,
        backgroundSize: 'cover',
        backgroundColor: '#0E090E',
      }}
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className={'font-[<PERSON>] text-xl text-white'}>{title}</h2>
        <button onClick={onClose} className="text-white/60 hover:text-white">
          <X size={24} />
        </button>
      </div>
      {children}
    </div>
  </div>
);
