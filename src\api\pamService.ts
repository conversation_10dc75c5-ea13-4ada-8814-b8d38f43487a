import axios from 'axios'

export interface BalanceUpdateRequest {
  amount: string
  component: string
  currency: string
  reason: string
  reference: string
  user_id: string
}

interface PamAuthRequest {
  service_id: string
  service_secret: string
}

interface PamAuthResponse {
  token: string
}

const PAM_BASE_URL = process.env.VITE_PAM_BASE_URL
const PAM_SERVICE_ID = process.env.VITE_PAM_SERVICE_ID
const PAM_SERVICE_SECRET = process.env.VITE_PAM_SERVICE_SECRET

let authToken: string | null = null
let authenticating = false

const authenticate = async (): Promise<void> => {
  if (authenticating || authToken) return
  authenticating = true

  try {
    const req: PamAuthRequest = {
      service_id: PAM_SERVICE_ID || '',
      service_secret: PAM_SERVICE_SECRET || '',
    }

    const response = await axios.post<PamAuthResponse>(`${PAM_BASE_URL}/api/adds/signin`, req, {
      headers: {
        'Content-Type': 'application/json',
      },
    })

    authToken = response.data.token
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error('PAM authentication failed:', error.response?.data || error.message)
    throw error
  } finally {
    authenticating = false
  }
}

export const updatePamBalance = async (payload: BalanceUpdateRequest): Promise<void> => {
  try {
    if (!authToken) {
      await authenticate()
    }

    await axios.post(`${PAM_BASE_URL}/api/adds/balance/update`, payload, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
    })
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    if (error.response?.status === 401) {
      console.warn('PAM token expired, retrying...')
      authToken = null
      await authenticate()

      return updatePamBalance(payload)
    }

    console.error('Failed to update PAM balance:', error.response?.data || error.message)
    throw error
  }
}
