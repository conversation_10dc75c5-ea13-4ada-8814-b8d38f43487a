/**
 * RollDiceGame Component
 * A dice rolling betting game where users place a bet and win based on a range guess.
 */
import React, { useState, useEffect } from 'react';
import { ChevronLeft, Coins, Dice1, History } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSocketContext } from '../context/socketProvider';
import { useMutation, useInfiniteQuery } from '@tanstack/react-query';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';
import { PlaceRollDiceBet, getGameHistory } from '../api/rollDaDiceService';
import dayjs from 'dayjs';
import SoundManager from '../components/soundManager/SoundManager';
import winSound from '../Sounds/RolldaDice/Win_Sound.m4a';
import BackGroundSound from '../Sounds/RolldaDice/Dice_Roll.mp3';
import Transactions from '../components/Transactions';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from '../components/GameLoadingScreen/GameLoadingScreen';
import { BET_ERROR, ToastTypes } from '../constant/strings';
import { COLORS } from '../constant/theming';
import { PzButton } from '../components/Shared/PzButton';
import { PzErrorPopup } from '../components/Shared/PzErrorPopup';

import { DEFAULT_BET } from '../constant/numbers';
interface RollDiceGameProps {
  onWin: (reward: { type: string; amount: number }) => void;
  onPurchase: (cost: number) => void;
  balance: number | null;
}

/**
 * Format coin values with locale separators.
 * @param {number|string} value - The coin amount to format
 * @returns {string}
 */
const formatCoins = (value: number | string) => Number(value).toLocaleString();

const RollDiceGame: React.FC<RollDiceGameProps> = ({
  onWin,
  onPurchase,
  balance,
}) => {
  const { accessToken } = useAuth();
  const { socketState, isConnected } = useSocketContext();
  const [showTransactionModal, setShowTransactionModal] = useState(false);

  const navigate = useNavigate();

  // Sound state management
  const [didWin, setDidWin] = useState(false);
  const [playDiceSound, setPlayDiceSound] = useState(false);

  // Bet and game state
  const [currentBet, setCurrentBet] = useState<number | string>(DEFAULT_BET);
  const [currentValue, setCurruntValue] = useState<string>('??');
  const [isRolling, setIsRolling] = useState<boolean>(false);
  const [multiplier, setMultiplier] = useState<number>(0);
  const [rangeStart, setRangeStart] = useState<number>(50);
  const [rangeEnd, setRangeEnd] = useState<number>(100);
  const [showWinAnim, setShowWinAnim] = useState<boolean>(false);
  const [showFundsErrorPopup, setShowFundsErrorPopup] = useState(false);
  // Constants for bet limits
  const MIN_BET = 1;
  const MAX_BET = Math.min(Number(balance), 1000);



  /**
   * On component mount, reset scroll and displayed value
   */
  useEffect(() => {
    window.scrollTo(0, 0);
    setCurruntValue('??');
  }, []);

  /**
   * Fetch game history pages
   */
  const {
    data: historyPages,
    refetch,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isHistoryLoading,
  } = useInfiniteQuery({
    queryKey: ['history'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error('Access token missing');
      return getGameHistory(
        { page: pageParam as number, per_page: 10 },
        accessToken
      );
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      if (
        !lastPage?.data?.total_pages ||
        allPages.length >= lastPage.data.total_pages
      )
        return undefined;
      return allPages.length + 1;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
  });

  /**
   * Handle real-time socket updates for roll events
   */
  useEffect(() => {
    if (!socketState) return;

    // When a new value is emitted, show rolling state and start dice sound
    if (socketState.current_value) {
      setCurruntValue(socketState.current_value);
      setIsRolling(true);
      setPlayDiceSound(true); // Start dice rolling sound
    }

    // Handle win status
    if (socketState.won_status === 'WON') {
      setDidWin(true); // Trigger win sound
      setPlayDiceSound(false); // Stop dice sound
      refetch();
      onWin({ type: 'coins', amount: Number(socketState.won_amount) });

      const timeout = setTimeout(() => {
        setIsRolling(false);
        setDidWin(false); // Reset win sound trigger
        setCurruntValue('??');
      }, 2000);
      return () => clearTimeout(timeout);
    }

    // Handle lose status
    if (socketState.won_status === 'LOSE') {
      setDidWin(false); // No win sound for losses
      setPlayDiceSound(false); // Stop dice sound
      refetch();

      const timeout = setTimeout(() => {
        setIsRolling(false);
        setCurruntValue('??');
      }, 2000);
      return () => clearTimeout(timeout);
    }
  }, [socketState, refetch]);

  /**
   * Stop dice sound when rolling finishes (backup cleanup)
   */
  useEffect(() => {
    if (!isRolling && playDiceSound) {
      // Stop dice sound when rolling stops
      const soundTimeout = setTimeout(() => {
        setPlayDiceSound(false);
      }, 500);
      return () => clearTimeout(soundTimeout);
    }
  }, [isRolling, playDiceSound]);

  /**
   * Ensure range start and end are within 0-100 and valid
   */
  const handleRangeChange = (start: number, end: number) => {
    if (start >= 0 && start <= 100 && end >= 0 && end <= 100 && start !== end) {
      setRangeStart(Math.min(start, end));
      setRangeEnd(Math.max(start, end));
    }
  };

  /**
   * Quick preset range buttons handler
   */
  const handleQuickRange = (start: number, end: number) => {
    handleRangeChange(start, end);
  };

  /**
   * Mutation to place a dice roll bet
   */
  const placeBetMutation = useMutation({
    mutationFn: async () => {
      if (Number(currentBet) > Number(balance)) return;
      if (!accessToken) throw new Error('token is missing');

      const result = await PlaceRollDiceBet(
        {
          bet_amount: Math.floor(Number(currentBet)),
          timestamp: new Date(),
          user_guess_end_point: rangeEnd,
          user_guess_start_point: rangeStart,
        },
        accessToken
      );

      if (result) {
        setMultiplier(result.data?.multiplier);
      }
      onPurchase(Number(currentBet));
    },
    onSuccess: () => {
      setTimeout(() => window.scrollTo({ top: 0, behavior: 'smooth' }), 10);
      CustomToast('success', 'Bet placed! Good luck!');
      setIsRolling(false);

      // Reset sound states when new bet is placed
      setDidWin(false);
      setPlayDiceSound(false);
    },
    onError: () => CustomToast(ToastTypes.ERROR, BET_ERROR),
  });

  /**
   * Track result text color based on win/lose status
   */
  const [resultColor, setResultColor] = useState('text-white');
  useEffect(() => {
    if (socketState?.won_status === 'WON') {
      setShowWinAnim(true);
      setResultColor('text-green-400');
      const colorTimeout = setTimeout(() => {
        setShowWinAnim(false);
        setResultColor('text-white');
      }, 1000);
      return () => clearTimeout(colorTimeout);
    } else if (socketState?.won_status === 'LOSE') {
      setShowWinAnim(true);
      setResultColor('text-red-400');
      const colorTimeout = setTimeout(() => {
        setShowWinAnim(false);
        setResultColor('text-white');
      }, 1000);
      return () => clearTimeout(colorTimeout);
    }
  }, [socketState?.won_status]);

  const onClose = () => {
    setShowTransactionModal(false);
  };

  const { isGameDataLoading } = useGameDataLoading(
    !isHistoryLoading,
    isConnected,
    Number.isFinite(balance)
  );

  const goToHomePage = () => navigate('/');

  const onPlaceBetClick = () => {
    Number(balance) < Number(currentBet)
      ? setShowFundsErrorPopup(true)
      : placeBetMutation.mutate();
  };

  return isGameDataLoading ? (
    <GameLoadingScreen />
  ) : (
    <div className="min-h-screen text-text-primary relative">
      <SoundManager
        sounds={{
          background: BackGroundSound,
          win: winSound,
        }}
        // Play dice rolling sound while dice is rolling
        backgroundKey={playDiceSound ? 'background' : null}
        // Play win sound when player wins
        playKey={didWin ? 'win' : undefined}
        volumes={{
          background: 0.6, // Slightly higher volume for dice rolling
          win: 1.0,
        }}
      />

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      <div className="max-w-4xl mx-auto relative">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={goToHomePage}
            className="text-white/60 hover:text-white transition-colors"
          >
            <ChevronLeft size={24} />
          </button>
          <h1 className="text-2xl font-bold">Roll da Dice</h1>
        </div>

        {/* Game Container */}
        <div className="space-y-6">
          {/* Main Game Area */}
          <div className="space-y-6">
            {/* Roll Display */}
            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Coins
                    size={20}
                    color={COLORS.primary}
                    className="text-yellow-400"
                  />
                  <span>{balance} Bucks</span>
                </div>
                {/* Show rolling indicator */}
                {isRolling && (
                  <div
                    className={`flex items-center gap-2 text-[${COLORS.primary}]`}
                  >
                    <Dice1 className="animate-spin" size={20} />
                    <span className="text-sm">Rolling...</span>
                  </div>
                )}
              </div>

              <div className="flex flex-col items-center justify-center min-h-[200px] relative">
                <div
                  className={`text-8xl font-bold mb-4 transition-all duration-300 ${resultColor} ${
                    isRolling ? 'animate-pulse' : ''
                  }`}
                >
                  {currentValue}
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-sm text-white/60">
                    Multiplier: {Number(multiplier).toFixed(2)}x
                  </div>
                </div>
              </div>
              {showWinAnim && (
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse">
                  <div
                    className={`text-3xl font-bold ${
                      socketState?.won_status === 'WON'
                        ? 'text-green-400'
                        : 'text-red-400'
                    }`}
                  >
                    +{socketState?.won_amount} coins
                  </div>
                </div>
              )}

              <div className="mt-6">
                <div className="h-2 bg-white/10 rounded-full overflow-hidden relative">
                  <div
                    className={`absolute h-full bg-[${COLORS.primary}]`}
                    style={{
                      left: `${rangeStart}%`,
                      width: `${rangeEnd - rangeStart}%`,
                    }}
                  />
                </div>
                <div className="flex justify-between mt-2 text-sm text-white/60">
                  <span>0</span>
                  <span>50</span>
                  <span>100</span>
                </div>
              </div>

              {/* Quick Range Buttons */}
              <div className="grid grid-cols-4 gap-2 mt-4">
                <button
                  onClick={() => handleQuickRange(0, 49)}
                  disabled={isRolling}
                  className="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-2 text-sm disabled:opacity-50"
                >
                  Under 50
                </button>
                <button
                  onClick={() => handleQuickRange(51, 100)}
                  disabled={isRolling}
                  className="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-2 text-sm disabled:opacity-50"
                >
                  Over 50
                </button>
                <button
                  onClick={() => handleQuickRange(25, 75)}
                  disabled={isRolling}
                  className="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-2 text-sm disabled:opacity-50"
                >
                  Middle 50
                </button>
                <button
                  onClick={() => handleQuickRange(0, 25)}
                  disabled={isRolling}
                  className="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-2 text-sm disabled:opacity-50"
                >
                  Low Risk
                </button>
              </div>
            </div>

            <PzButton
              text={isRolling ? 'Rolling...' : 'Roll Dice'}
              onClick={onPlaceBetClick}
              isDisabled={isRolling}
            />

            {/* Controls */}
            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              <div className="space-y-6">
                {/* Range Controls */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Range Start
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={rangeStart}
                        disabled={isRolling}
                        onChange={(e) =>
                          handleRangeChange(
                            parseInt(e.target.value) || 0,
                            rangeEnd
                          )
                        }
                        className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-${COLORS.primary} [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield] disabled:opacity-50`}
                        min="0"
                        max="99"
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                        <button
                          onClick={() =>
                            handleRangeChange(rangeStart - 1, rangeEnd)
                          }
                          disabled={isRolling}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          -
                        </button>
                        <button
                          onClick={() =>
                            handleRangeChange(rangeStart + 1, rangeEnd)
                          }
                          disabled={isRolling}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Range End
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={rangeEnd}
                        disabled={isRolling}
                        onChange={(e) =>
                          handleRangeChange(
                            rangeStart,
                            parseInt(e.target.value) || 100
                          )
                        }
                        className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-${COLORS.primary} [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield] disabled:opacity-50`}
                        min="1"
                        max="100"
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                        <button
                          onClick={() =>
                            handleRangeChange(rangeStart, rangeEnd - 1)
                          }
                          disabled={isRolling}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          -
                        </button>
                        <button
                          onClick={() =>
                            handleRangeChange(rangeStart, rangeEnd + 1)
                          }
                          disabled={isRolling}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Bet Amount */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm text-white/60">
                      Bet Amount
                    </label>
                    {/* <AnimatedInfoButton
                      onClick={() => setShowRulesModal(true)}
                      label="Game Rules"
                      size={16}
                      className="text-xs"
                    /> */}
                  </div>
                  <div className="relative">
                    <input
                      type="number"
                      inputMode="numeric"
                      value={currentBet}
                      disabled={isRolling}
                      onChange={(e) => setCurrentBet(e.target.value || '')}
                      className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-${COLORS.primary} [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield] disabled:opacity-50`}
                    />
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                      <button
                        onClick={() =>
                          setCurrentBet((prev) =>
                            Math.max(MIN_BET, Number(prev) / 2)
                          )
                        }
                        disabled={isRolling}
                        className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                      >
                        ½
                      </button>
                      <button
                        onClick={() =>
                          setCurrentBet((prev) =>
                            Math.min(Number(prev) * 2, MAX_BET)
                          )
                        }
                        disabled={isRolling}
                        className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors disabled:opacity-50"
                      >
                        2×
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* History */}
          <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
            <h2 className="text-lg text-[Anton] mb-4 flex items-center gap-2">
              <History size={20} className={`text-${COLORS.primary}`} />
              Game History
            </h2>

            <div className="space-y-4">
              {historyPages?.pages[0]?.data?.histories &&
                historyPages?.pages
                  .flatMap((page) => page.data?.histories || [])
                  .map((game, index) => {
                    const lost = game.won_status === 'LOSE';
                    const profit = parseFloat(game.won_amount);
                    return (
                      <div
                        key={index}
                        className={`p-4 rounded-lg text-[Poppins] ${
                          game.won_status === 'WON'
                            ? 'bg-green-500/10'
                            : 'bg-red-500/10'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Dice1
                              size={16}
                              className={
                                game.won_status === 'WON'
                                  ? 'text-green-400'
                                  : 'text-red-400'
                              }
                            />
                            <span>
                              Rolled {parseFloat(game.crash_point).toFixed(0)}
                            </span>
                            <span className="text-white/60">
                              ({Number(game.multiplier).toFixed(2)}x)
                            </span>
                          </div>
                          {lost ? (
                            <span className={'text-red-400'}>
                              {'-'}
                              {formatCoins(game?.bet_amount)}
                            </span>
                          ) : (
                            <span className={'text-green-400'}>
                              {profit > 0 ? '+' : ''}
                              {formatCoins(profit)}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center justify-between text-sm text-white/60">
                          <span>
                            Bet: {parseFloat(game.bet_amount).toFixed(0)}
                          </span>
                          <span>{dayjs(game.timestamp).format('hh:mm A')}</span>
                        </div>
                      </div>
                    );
                  })}

              {hasNextPage && (
                <PzButton
                  text="View All"
                  onClick={() => {
                    setShowTransactionModal(true);
                  }}
                  isDisabled={isFetchingNextPage}
                />
              )}

              {historyPages?.pages[0]?.data?.histories &&
                historyPages?.pages[0]?.data?.histories.length === 0 && (
                  <div className="text-center text-white/40 py-8">
                    <History size={48} className="mx-auto mb-4 opacity-40" />
                    <p>No games played yet</p>
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>
      {showTransactionModal && <Transactions onClose={onClose} />}
      {showFundsErrorPopup && (
        <PzErrorPopup
          setShowFundsErrorPopup={setShowFundsErrorPopup}
          cost={Number(currentBet)}
        />
      )}

    </div>
  );
};

export default RollDiceGame;
