export function throttle(func: (args: IArguments | any) => any, delay: number) {
  const throttleParams = {
    inThrottle: false,
    lastFn: 0,
    lastTime: 0,
  };

  const { inThrottle, lastFn, lastTime } = throttleParams;

  return function (this: typeof throttle) {
    const context = this;
    const args = arguments;

    if (!inThrottle) {
      func.apply(context, args as IArguments as any);
      throttleParams.lastTime = Date.now();
      throttleParams.inThrottle = true;
    } else {
      clearTimeout(lastFn);
      throttleParams.lastFn = window.setTimeout(function () {
        if (Date.now() - lastTime >= delay) {
          func.apply(context, args as IArguments as any);
          throttleParams.lastTime = Date.now();
        }
      }, Math.max(delay - (Date.now() - lastTime), 0));
    }
  };
}
