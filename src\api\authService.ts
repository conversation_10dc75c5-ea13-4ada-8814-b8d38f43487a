// src/api/authService.ts
import axios, { AxiosError } from 'axios';
import {
  REGISTER_API,
  LOGIN_API,
  FORGOT_PASSWORD_API,
  RESET_PASSWORD_API,
  GOOGLE_AUTH_API,
  VERIFY_OTP_API
} from './auth'; // Adjust the import path as necessary
import { toast } from 'react-toastify';

// Interfaces for request data
interface LoginData {
  email: string;
  password: string;
}

export interface SignupPayload {
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  password: string;
  isAdult: boolean;
  refered_by_code?: string;
}

interface ForgotPasswordData {
  username: string;
}

interface VerifyOtpData {
  username: string;
  otp: string;
}

interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// API call functions
export const login = async ({ email, password }: LoginData): Promise<any> => {
  try {
    const response = await axios.post(LOGIN_API, { login_id: email, password });
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    throw err.response?.data || 'Login failed';
  }
};
  
export const signup = async (data: SignupPayload): Promise<any> => {
  try {
    const response = await axios.post(REGISTER_API, data);
    return response.data;
  } catch (error: any) {
    // extract the message from the server response
    const serverMsg = error.response?.data?.message || 'Signup failed';
    // show it as a toast
    toast.error(serverMsg);
    // re-throw so that upstream can also catch it
    throw new Error(serverMsg);
  }
};

export const forgotPassword = async ({ username }: ForgotPasswordData): Promise<any> => {
  try {
    const response = await axios.post(FORGOT_PASSWORD_API, { username });
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    throw err.response?.data || 'Forgot password failed';
  }
};

export const verifyOtp = async ({ username, otp }: VerifyOtpData): Promise<any> => {
  try {

    const response = await axios.post(VERIFY_OTP_API, { username, otp });
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    throw err.response?.data || 'OTP verification failed';
  }
};

export const resetPassword = async ({ token, newPassword, confirmPassword }: ResetPasswordData): Promise<any> => {
  try {
    const response = await axios.post(RESET_PASSWORD_API, { token, new_password: newPassword, confirm_password: confirmPassword });
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    throw err.response?.data || 'Password reset failed';
  }
};

export const googleAuth = async (): Promise<any> => {
  try {
    const response = await axios.get(GOOGLE_AUTH_API);
    return response.data;
  } catch (error) {
    const err = error as AxiosError;
    throw err.response?.data || 'Google authentication failed';
  }
};
