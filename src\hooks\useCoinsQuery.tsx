// src/context/CoinsContext.tsx
import React, { createContext, useContext, ReactNode } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { GET_USER_COINS } from '../api/auth';
import { useAuth } from '../auth/AuthContext';

interface CoinsContextType {
  coins: number;
  isLoading: boolean;
  error: Error | null;
  updateCoins: (newAmount: number) => void;
  addCoins: (amount: number) => void;
  subtractCoins: (amount: number) => void;
  refetchCoins: () => void;
}

const CoinsContext = createContext<CoinsContextType | undefined>(undefined);

export const CoinsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { accessToken } = useAuth();
  const queryClient = useQueryClient();

  // Main coins query
  const {
    data,
    isLoading,
    error,
    refetch: refetchCoins
  } = useQuery({
    queryKey: ['coins'],
    queryFn: () =>
      fetch(GET_USER_COINS, { headers: { Authorization: `Bearer ${accessToken}` } })
        .then((res) => res.json()),
    // refetchInterval: 5000,
    refetchOnWindowFocus: true,
    enabled:!! accessToken
  });

  // Current coins value (safely extracted)
  const coins = data?.data?.point;

  // Function to set coins to a specific value
  const updateCoins = (newAmount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: newAmount
      }
    }));
  };

  // Function to add coins
  const addCoins = (amount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: (oldData?.data?.point || 0) + amount
      }
    }));
  };

  // Function to subtract coins
  const subtractCoins = (amount: number) => {
    queryClient.setQueryData(['coins'], (oldData: any) => ({
      ...oldData,
      data: {
        ...oldData?.data,
        point: Math.max(0, (oldData?.data?.point || 0) - amount)
      }
    }));
  };

  const value = {
    coins,
    isLoading,
    error,
    updateCoins,
    addCoins,
    subtractCoins,
    refetchCoins
  };

  return <CoinsContext.Provider value={value}>{children}</CoinsContext.Provider>;
};

export const useCoins = (): CoinsContextType => {
  const context = useContext(CoinsContext);
  if (context === undefined) {
    throw new Error('useCoins must be used within a CoinsProvider');
  }
  return context;
};