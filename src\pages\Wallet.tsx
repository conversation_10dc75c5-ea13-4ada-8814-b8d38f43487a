import React from 'react';
import { useNavigate } from 'react-router-dom';
import WalletComponent from '../components/Wallet';

interface WalletPageProps {
  balance: {
    coins: number;
    fpp: number;
    real_money:string
  };
}

const WalletPage = ( ) => {
  const navigate = useNavigate();

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Wallet</h1>
      <WalletComponent 
        balance={{
          coins: 0,
          fpp: 0,
          real_money: "0",
          points: 0
        }}
        onClose={() => navigate('/')} 
      />
    </div>
  );
};

export default WalletPage;