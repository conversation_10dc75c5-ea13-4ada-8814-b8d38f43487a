import React, { useState } from 'react';
import { AnimatedInfoButton } from '../Shared/AnimatedInfoButton';
import { GameRulesModal } from '../Shared/GameRulesModal';

// Example of how different components can define their own rules
const plinkoRules = [
  {
    title: "Drop the Ball",
    description: "Click to drop a ball from the top of the Plinko board. Watch it bounce through the pegs!"
  },
  {
    title: "Multipliers",
    description: "Each slot at the bottom has a different multiplier. The center slots typically have higher multipliers."
  },
  {
    title: "Winning",
    description: "Your winnings are calculated as: Bet Amount × Slot Multiplier where your ball lands."
  }
];



const cryptoKingRules = [
  {
    title: "Market Prediction",
    description: "Predict whether the crypto price will go up or down within the time limit."
  },
  {
    title: "Timing",
    description: "You have a limited time to make your prediction before the market closes."
  },
  {
    title: "Payouts",
    description: "Correct predictions earn you coins based on the current market volatility."
  }
];

const scratchCardRules = [
  {
    title: "Scratch to Reveal",
    description: "Click on the cards to scratch them and reveal the symbols underneath."
  },
  {
    title: "Matching Symbols",
    description: "Match 3 symbols in a row (horizontal, vertical, or diagonal) to win!"
  },
  {
    title: "Instant Win",
    description: "Some cards have instant win symbols that give you immediate rewards."
  }
];

export const GameRulesExample: React.FC = () => {
  const [showPlinkoRules, setShowPlinkoRules] = useState(false);
  const [showCryptoRules, setShowCryptoRules] = useState(false);
  const [showScratchRules, setShowScratchRules] = useState(false);

  return (
    <div className="p-6 bg-surface-card/90 backdrop-blur-sm rounded-2xl">
      <h2 className="text-xl font-bold mb-6 text-white font-[Anton]">
        Game Rules Examples
      </h2>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
          <div>
            <h3 className="font-semibold text-white">Plinko Game</h3>
            <p className="text-sm text-white/60">Drop balls and win big!</p>
          </div>
          <AnimatedInfoButton 
            onClick={() => setShowPlinkoRules(true)}
            label="Rules"
            size={18}
          />
        </div>

        <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
          <div>
            <h3 className="font-semibold text-white">Crypto King</h3>
            <p className="text-sm text-white/60">Predict the market!</p>
          </div>
          <AnimatedInfoButton 
            onClick={() => setShowCryptoRules(true)}
            label="Rules"
            size={18}
          />
        </div>

        <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
          <div>
            <h3 className="font-semibold text-white">Scratch Cards</h3>
            <p className="text-sm text-white/60">Scratch and match to win!</p>
          </div>
          <AnimatedInfoButton 
            onClick={() => setShowScratchRules(true)}
            label="Rules"
            size={18}
          />
        </div>
      </div>

      {/* Modals for each game */}
      <GameRulesModal
        isOpen={showPlinkoRules}
        onClose={() => setShowPlinkoRules(false)}
        gameTitle="Plinko"
        rules={plinkoRules}
      />

      <GameRulesModal
        isOpen={showCryptoRules}
        onClose={() => setShowCryptoRules(false)}
        gameTitle="Crypto King"
        rules={cryptoKingRules}
      />

      <GameRulesModal
        isOpen={showScratchRules}
        onClose={() => setShowScratchRules(false)}
        gameTitle="Scratch Cards"
        rules={scratchCardRules}
      />
    </div>
  );
};
