# Animated Info Button & Game Rules Modal

This directory contains reusable components for displaying game rules with an animated info button.

## Components

### AnimatedInfoButton

A continuously animated info button that pulses to draw attention.

**Props:**
- `onClick: () => void` - Function to call when button is clicked
- `className?: string` - Additional CSS classes
- `size?: number` - Size of the info icon (default: 20)
- `label?: string` - Text label next to the icon (default: "Info")

**Example:**
```tsx
import { AnimatedInfoButton } from '../Shared/AnimatedInfoButton';

<AnimatedInfoButton 
  onClick={() => setShowRules(true)}
  label="Game Rules"
  size={18}
/>
```

### GameRulesModal

A modal component that displays game rules passed as props.

**Props:**
- `isOpen: boolean` - Whether the modal is open
- `onClose: () => void` - Function to call when modal should close
- `gameTitle: string` - Title of the game
- `rules: GameRule[]` - Array of rule objects
- `className?: string` - Additional CSS classes

**GameRule Interface:**
```tsx
interface GameRule {
  title: string;
  description: string;
}
```

**Example:**
```tsx
import { GameRulesModal } from '../Shared/GameRulesModal';

const gameRules = [
  {
    title: "How to Play",
    description: "Choose a range and place your bet..."
  },
  {
    title: "Winning",
    description: "If the result falls in your range, you win!"
  }
];

<GameRulesModal
  isOpen={showRulesModal}
  onClose={() => setShowRulesModal(false)}
  gameTitle="My Game"
  rules={gameRules}
/>
```

## Usage Pattern

1. **Define your game rules** as an array of objects with title and description
2. **Add state** to control modal visibility
3. **Place AnimatedInfoButton** where you want users to access rules
4. **Add GameRulesModal** to your component with the rules data

## Features

- **Responsive Design**: Works on both mobile and desktop
- **Smooth Animations**: Fade in/out with scale and slide effects
- **Continuous Animation**: Info button pulses continuously to draw attention
- **Accessible**: Proper ARIA labels and keyboard navigation
- **Customizable**: Easy to style and modify for different games
- **Reusable**: Pass different rules from any component

## Integration Example

See `src/pages/RollDiceGame.tsx` for a complete implementation example, or check `src/components/Examples/GameRulesExample.tsx` for multiple game examples.
