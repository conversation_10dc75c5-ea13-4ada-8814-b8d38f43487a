import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Trophy, Star, Target, X, Medal, Sparkles, Gift, CheckCircle } from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import useFetch from '../hooks/useFetch';
import { GET_USER_COINS } from '../api/auth';

/**
 * Formats a number or numeric string into a localized string with commas.
 * @param {number | string} value - The value to format.
 * @returns {string} Formatted number string.
 */
const formatCoins = (value: number | string): string => Number(value).toLocaleString();

const ACHIEVEMENTS = [
  {
    id: 'first_win',
    title: 'First Victory',
    description: 'Win your first game',
    longDescription: 'Take your first step towards becoming a champion by winning your first game on Playzuzu.',
    reward: { type: 'fpp', amount: 100 },
    progress: 100,
    icon: Trophy,
    completed: true,
    banner: 'https://images.unsplash.com/photo-1560253023-3ec5d502959f?w=800&h=300&fit=crop',
    rules: [
      'Complete one full game session',
      'Achieve a winning outcome',
      'Must be completed in a standard game mode'
    ],
    terms: [
      'Achievement can only be earned once',
      'Game must be completed fairly without exploits',
      'Reward will be credited instantly upon completion'
    ]
  },
  {
    id: 'high_roller',
    title: 'High Roller',
    description: 'Win 1000 coins in a single game',
    longDescription: 'Go big or go home! Win 1000 coins in a single game session to prove your high-stakes gaming prowess.',
    reward: { type: 'fpp', amount: 200 },
    progress: 30,
    icon: Star,
    completed: false,
    banner: 'https://images.unsplash.com/photo-1518133910546-b6c2fb7d79e3?w=800&h=300&fit=crop',
    rules: [
      'Must be won in a single game session',
      'Bonus coins count towards total',
      'Multipliers are included in calculation'
    ],
    terms: [
      'Achievement progress resets each game',
      'Must be earned through legitimate gameplay',
      'Promotional coins do not count towards total'
    ]
  },
  {
    id: 'social',
    title: 'Social Butterfly',
    description: 'Refer 3 friends',
    longDescription: 'Spread the joy of gaming by introducing your friends to the Playzuzu community.',
    reward: { type: 'coins', amount: 1000 },
    progress: 0,
    icon: Target,
    completed: false,
    banner: 'https://images.unsplash.com/photo-*************-7986c2920216?w=800&h=300&fit=crop',
    rules: [
      'Referred friends must create accounts',
      'Friends must reach level 5',
      'Each friend must make at least one purchase'
    ],
    terms: [
      'Only unique referrals count',
      'Referred accounts must be active for 30 days',
      'Maximum of 10 referral rewards per month'
    ]
  }
];

const RewardsPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedAchievement, setSelectedAchievement] = useState<typeof ACHIEVEMENTS[0] | null>(null);
  const {accessToken} = useAuth();
  
    const {data} = useFetch(GET_USER_COINS,{
      headers:{
        Authorization : `Bearer ${accessToken}`
      }
    });
    const points = data?.data?.point
  

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/')}
          className="text-white/60 hover:text-white transition-colors"
        >
          <ChevronLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">Achievements</h1>
      </div>

      {/* Balance Display */}
      <div className="bg-white/5 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          {/* <div>
            <p className="text-sm text-white/60">Your Balance</p>
            <p className="text-lg font-bold">{balance.real_money} FPP</p>
          </div> */}
          <div>
            <p className="text-sm text-white/60">Buks</p>
            <p className="text-lg font-bold">{formatCoins(points)}</p>
          </div>
        </div>
      </div>

      {/* Achievements List */}
      <div className="space-y-4">
        {ACHIEVEMENTS.map(achievement => {
          const IconComponent = achievement.icon;
          return (
            <div
              key={achievement.id}
              onClick={() => setSelectedAchievement(achievement)}
              className={`bg-white/5 rounded-xl p-4 ${
                achievement.completed ? 'border border-green-500/20' : ''
              } cursor-pointer hover:bg-white/10 transition-colors`}
            >
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-xl ${
                  achievement.completed ? 'bg-green-500/20' : 'bg-white/10'
                }`}>
                  <IconComponent size={24} className={
                    achievement.completed ? 'text-green-400' : 'text-white/60'
                  } />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="font-medium flex items-center gap-2">
                        {achievement.title}
                        {achievement.completed && (
                          <CheckCircle size={16} className="text-green-400" />
                        )}
                      </h3>
                      <p className="text-sm text-white/60">{achievement.description}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-yellow-400">
                        {achievement.reward.amount} {achievement.reward.type === 'coins' ? 'Coins' : 'FPP'}
                      </p>
                    </div>
                  </div>
                  {!achievement.completed && (
                    <div className="mt-3">
                      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-yellow-400 transition-all duration-300"
                          style={{ width: `${achievement.progress}%` }}
                        />
                      </div>
                      <p className="text-sm text-white/60 mt-1">
                        Progress: {achievement.progress}%
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Achievement Details Modal */}
      {selectedAchievement && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl w-full max-w-2xl flex flex-col max-h-[90vh]">
            {/* Hero Banner - Fixed at top */}
            <div className="relative h-48 flex-shrink-0">
              <img 
                src={selectedAchievement.banner}
                alt={selectedAchievement.title}
                className="w-full h-full object-cover rounded-t-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-purple-900 to-transparent rounded-t-2xl" />
              <button 
                onClick={() => setSelectedAchievement(null)}
                className="absolute top-4 right-4 text-white/60 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
              <div className="absolute bottom-4 left-4 right-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-3 rounded-xl ${
                    selectedAchievement.completed ? 'bg-green-500/20' : 'bg-white/10'
                  }`}>
                    <selectedAchievement.icon size={24} className={
                      selectedAchievement.completed ? 'text-green-400' : 'text-white'
                    } />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold flex items-center gap-2">
                      {selectedAchievement.title}
                      {selectedAchievement.completed && (
                        <CheckCircle size={20} className="text-green-400" />
                      )}
                    </h2>
                    <p className="text-white/60">{selectedAchievement.description}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="overflow-y-auto flex-1 p-6">
              <div className="max-w-2xl mx-auto space-y-8">
                {/* Progress */}
                {!selectedAchievement.completed && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Progress</h3>
                    <div className="h-3 bg-white/10 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-yellow-400 transition-all duration-300"
                        style={{ width: `${selectedAchievement.progress}%` }}
                      />
                    </div>
                    <p className="text-sm text-white/60 mt-2">
                      {selectedAchievement.progress}% Complete
                    </p>
                  </div>
                )}

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Description</h3>
                  <p className="text-white/80 leading-relaxed">
                    {selectedAchievement.longDescription}
                  </p>
                </div>

                {/* Rules */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Rules</h3>
                  <ul className="space-y-3">
                    {selectedAchievement.rules.map((rule, index) => (
                      <li key={index} className="flex items-center gap-3 text-white/80">
                        <Medal size={16} className="text-yellow-400 flex-shrink-0" />
                        <span className="leading-relaxed">{rule}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Terms & Conditions */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Terms & Conditions</h3>
                  <ul className="space-y-3">
                    {selectedAchievement.terms.map((term, index) => (
                      <li key={index} className="flex items-center gap-3 text-white/80">
                        <Sparkles size={16} className="text-blue-400 flex-shrink-0" />
                        <span className="leading-relaxed">{term}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Reward */}
                <div className="bg-white/5 rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <Gift size={20} className="text-yellow-400" />
                    Reward
                  </h3>
                  <div className="flex items-center justify-between">
                    <p className="text-white/80">Complete this achievement to earn:</p>
                    <p className="text-xl font-bold text-yellow-400">
                      {selectedAchievement.reward.amount} {selectedAchievement.reward.type === 'coins' ? 'Coins' : 'FPP'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RewardsPage;