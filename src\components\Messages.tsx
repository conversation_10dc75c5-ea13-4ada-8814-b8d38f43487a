import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Co<PERSON>, Star, MessageCircle, Trash2, Eye, Loader2 } from 'lucide-react';
import {
  getNotification,
  markAllReadNotification,
  markReadNotificationByID,
  deletNotificationById,
} from '../api/notificationService';
import { useAuth } from '../auth/AuthContext';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface MessagesProps {
  onClose: () => void;
}

interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'Reward' | 'Achievement' | 'Bonus' | 'System';
  read: boolean;
  created_at: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

// Icon mapping for different notification types
const NOTIFICATION_ICONS = {
  Reward: Gift,
  Achievement: Star,
  Bonus: Coins,
  System: MessageCircle,
} as const;

// Pagination configuration
const PAGE_LIMIT = 10;

// Animation timing
const ANIMATION_DELAY = 50;
const CLOSE_ANIMATION_DURATION = 300;

// Responsive breakpoint
const MOBILE_BREAKPOINT = 768;

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const Messages: React.FC<MessagesProps> = ({ onClose }) => {
  // ========================================
  // STATE MANAGEMENT
  // ========================================
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  
  const { accessToken } = useAuth();

  // ========================================
  // LIFECYCLE EFFECTS
  // ========================================
  
  // Initialize component - fetch initial notifications
  useEffect(() => {
    fetchNotifications(1, true);
  }, []);

  // Setup responsive behavior and animations
  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger entrance animation
    const animationTimer = setTimeout(() => setIsAnimating(true), ANIMATION_DELAY);

    // Handle escape key to close modal
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') handleClose();
    };
    window.addEventListener('keydown', handleEscape);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('keydown', handleEscape);
      clearTimeout(animationTimer);
    };
  }, []);

  // ========================================
  // API FUNCTIONS
  // ========================================
  
  /**
   * Fetch notifications with pagination
   * @param pageNumber - Page number to fetch
   * @param reset - Whether to reset the notifications list
   */
  const fetchNotifications = async (pageNumber: number, reset = false) => {
    if (!accessToken || loading) return;
    
    try {
      setLoading(true);
      const response = await getNotification(
        { page: pageNumber, per_page: PAGE_LIMIT }, 
        accessToken
      );
      
      // Update notifications list
      if (reset) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }
      
      // Update pagination state
      setHasMore(response.notifications.length === PAGE_LIMIT);
      setPage(pageNumber);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Mark all notifications as read
   */
  const handleMarkAllRead = async () => {
    if (!accessToken) return;
    
    try {
      await markAllReadNotification(accessToken);
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  /**
   * Mark a specific notification as read
   * @param id - Notification ID
   */
  const handleMarkAsRead = async (id: string) => {
    if (!accessToken) return;
    
    try {
      await markReadNotificationByID(id, accessToken);
      setNotifications(prev =>
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: true } 
            : notification
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  /**
   * Delete a specific notification
   * @param id - Notification ID
   */
  const handleDelete = async (id: string) => {
    if (!accessToken) return;
    
    try {
      await deletNotificationById(id, accessToken);
      setNotifications(prev => 
        prev.filter(notification => notification.id !== id)
      );
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // ========================================
  // EVENT HANDLERS
  // ========================================
  
  /**
   * Handle modal close with animation
   */
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(onClose, CLOSE_ANIMATION_DURATION);
  };

  /**
   * Load more notifications (pagination)
   */
  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchNotifications(page + 1);
    }
  };

  // ========================================
  // STYLING CLASSES
  // ========================================
  
  // Dynamic container classes based on device type and animation state
  const containerClass = isMobile
    ? `fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center transition-all duration-300 ease-out ${
        isAnimating ? 'opacity-100' : 'opacity-0'
      }`
    : `fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out ${
        isAnimating ? 'opacity-100' : 'opacity-0'
      }`;

  // Dynamic content classes for responsive design and animations
  const contentClass = isMobile
    ? `bg-gradient-to-b from-[#510957] to-black rounded-t-3xl w-full max-h-[85vh] flex flex-col transform transition-transform duration-300 ease-out ${
        isAnimating ? 'translate-y-0' : 'translate-y-full'
      }`
    : `bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-md max-h-[85vh] flex flex-col transform transition-all duration-300 ease-out ${
        isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`;

  // ========================================
  // RENDER FUNCTIONS
  // ========================================
  
  /**
   * Render individual notification item
   */
  const renderNotificationItem = (notification: Notification) => {
    const Icon = NOTIFICATION_ICONS[notification.type] || MessageCircle;
    
    return (
      <div
        key={notification.id}
        className={`p-6 hover:bg-white/10 transition-colors ${
          !notification.read ? 'bg-white/10' : ''
        }`}
      >
        <div className="flex items-start gap-4">
          {/* Notification Icon */}
          <div className="p-3 rounded-xl bg-white/10">
            <Icon size={24} className="text-yellow-400" />
          </div>
          
          {/* Notification Content */}
          <div className="flex-1">
            {/* Title and Actions */}
            <div className="flex justify-between items-start mb-1">
              <h3 className="font-medium text-white">
                {notification.title}
                {!notification.read && (
                  <span className="ml-2 inline-block px-2 py-0.5 text-xs bg-yellow-400 text-black rounded-full">
                    New
                  </span>
                )}
              </h3>
              
              {/* Action Buttons */}
              <div className="flex gap-2">
                {!notification.read && (
                  <Eye 
                    size={16} 
                    className="text-white/50 cursor-pointer hover:text-white transition-colors" 
                    onClick={() => handleMarkAsRead(notification.id)}
                    // title="Mark as read"
                  />
                )}
                <Trash2 
                  size={16} 
                  className="text-red-400 cursor-pointer hover:text-red-300 transition-colors" 
                  onClick={() => handleDelete(notification.id)}
                  // title="Delete notification"
                />
              </div>
            </div>
            
            {/* Content and Timestamp */}
            <p className="text-sm text-white/60 mb-2">{notification.content}</p>
            <p className="text-xs text-white/40">
              {new Date(notification.created_at).toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    );
  };

  /**
   * Render loading state
   */
  const renderLoadingState = () => (
    <div className="p-8 text-center text-white/60">
      <Loader2 size={40} className="mx-auto mb-4 animate-spin opacity-40" />
      <p>Loading messages...</p>
    </div>
  );

  /**
   * Render empty state
   */
  const renderEmptyState = () => (
    <div className="p-8 text-center text-white/60">
      <MessageCircle size={40} className="mx-auto mb-4 opacity-40" />
      <p>No messages yet</p>
    </div>
  );

  /**
   * Render load more button
   */
  const renderLoadMoreButton = () => (
    <div className="p-4 text-center">
      <button
        onClick={handleLoadMore}
        disabled={loading}
        className="text-white/70 hover:text-white px-4 py-2 border border-white/20 rounded-lg transition-colors disabled:opacity-50"
      >
        {loading ? (
          <>
            <Loader2 size={18} className="animate-spin inline-block mr-2" />
            Loading...
          </>
        ) : (
          'Load More'
        )}
      </button>
    </div>
  );

  // ========================================
  // MAIN RENDER
  // ========================================
  
  return (
    <div className={containerClass}>
      <div className={contentClass}>
        {/* Header Section */}
        <div className={`p-6 flex justify-between items-center sticky top-0 bg-[#510957] ${
          isMobile ? 'rounded-t-3xl' : 'rounded-t-2xl'
        } z-10`}>
          {/* Title */}
          <div className="flex items-center gap-3">
            <Bell size={20} className="text-yellow-400" />
            <h2 className="text-xl font-bold text-white font-[Anton] tracking-wide">
              Messages
            </h2>
          </div>
          
          {/* Header Actions */}
          <div className="flex gap-3">
            <button 
              onClick={handleMarkAllRead} 
              className="text-sm text-white/60 hover:text-white transition-colors"
            >
              Mark All Read
            </button>
            <button 
              onClick={handleClose} 
              className="text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full"
              title="Close messages"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Mobile Handle Bar */}
        {isMobile && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-1 bg-white/30 rounded-full"></div>
          </div>
        )}

        {/* Content Section */}
        <div className="flex-1 overflow-y-auto divide-y divide-white/10">
          {notifications.length > 0 ? (
            <>
              {/* Notifications List */}
              {notifications.map(renderNotificationItem)}
              
              {/* Load More Button */}
              {hasMore && renderLoadMoreButton()}
            </>
          ) : loading ? (
            renderLoadingState()
          ) : (
            renderEmptyState()
          )}
        </div>
      </div>
    </div>
  );
};

export default Messages;
