import { ArrowLeft } from 'lucide-react';
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

const PrivacyPolicyPage: React.FC = () => {

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <div className="min-h-screen  bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] p-6 text-white">
      <header className="mb-8 flex items-center">
        <Link to="/" className="flex items-center gap-2 text-white hover:text-gray-300">
          <ArrowLeft size={24} />
        </Link>
          <div className='flex items-center justify-center  w-full'>
            <h1 className="text-3xl text-center tracking-wider font-[Anton]">Privacy Policy</h1>
          </div>
      </header>
      <div className="max-w-3xl mx-auto space-y-6">
        {/* <h1 className="text-3xl font-bold text-center mb-6">Privacy Policy</h1> */}

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[<PERSON>]">1. Information We Collect</h2>
          <p className="text-sm leading-relaxed">
            We collect information you provide directly to us, including:
          </p>
          <ul className="list-disc list-inside text-sm mt-2 space-y-1">
            <li>Account information (username, email)</li>
            <li>Game activity and preferences</li>
            <li>Device information and identifiers</li>
            <li>Usage data and analytics</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">2. How We Use Your Information</h2>
          <p className="text-sm leading-relaxed">
            We use the collected information to:
          </p>
          <ul className="list-disc list-inside text-sm mt-2 space-y-1">
            <li>Provide and improve our services</li>
            <li>Personalize your gaming experience</li>
            <li>Send important updates and notifications</li>
            <li>Prevent fraud and ensure fair gameplay</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">3. Data Security</h2>
          <p className="text-sm leading-relaxed">
            We implement appropriate security measures to protect your personal information from unauthorized access, alteration, or disclosure.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">4. Third-Party Services</h2>
          <p className="text-sm leading-relaxed">
            We may use third-party services for analytics, advertising, and payment processing. These services have their own privacy policies and data collection practices.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">5. Your Rights</h2>
          <p className="text-sm leading-relaxed">
            You have the right to:
          </p>
          <ul className="list-disc list-inside text-sm mt-2 space-y-1">
            <li>Access your personal information</li>
            <li>Request correction of inaccurate data</li>
            <li>Request deletion of your data</li>
            <li>Opt-out of marketing communications</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">6. Children's Privacy</h2>
          <p className="text-sm leading-relaxed">
            Our service is not intended for users under 18 years of age. We do not knowingly collect personal information from children.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">7. Changes to Privacy Policy</h2>
          <p className="text-sm leading-relaxed">
            We may update this privacy policy from time to time. We will notify you of any material changes through our service.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2  font-[Anton]">8. Contact Us</h2>
          <p className="text-sm leading-relaxed">
            If you have questions about this privacy policy, please contact our support team through the app.
          </p>
        </section>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
