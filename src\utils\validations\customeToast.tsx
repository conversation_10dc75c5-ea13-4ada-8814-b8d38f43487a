import { Flip, toast } from 'react-toastify';
import { COLORS } from '../../constant/theming';

export const CustomToast = (type: any, message: any, autoClose?: number) => {
  const toastOptions = {
    position: 'top-right',
    autoClose: autoClose || 1500,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: false,
    draggable: true,
    progress: undefined,
    theme: 'colored',
    transition: Flip,
    // Add custom style only for success type
    ...(type === 'success' && {
      style: {
        background: `linear-gradient(to right,${COLORS.primary}, #B516FF)`,
        color: '#000', // Optional: set text color
      },
    }),
  };

  return toast[type](message, toastOptions);
};
