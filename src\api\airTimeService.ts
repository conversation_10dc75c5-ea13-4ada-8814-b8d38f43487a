/**
 * AirTime Service API helper functions
 */
import axios from 'axios';
import {
  AIRTIME_ACTIVE_UTILITIES,
  CLAIM_AIRTIME,
  AIRTIME_TRANSACTIONS
} from './auth';

/**
 * Pagination parameters for history and utilities endpoints
 */
export interface HistoryData {
  /** Current page number */
  page: number;
  /** Items per page */
  per_page: number;
}

/**
 * Payload for claiming airtimes
 */
interface ClaimData {
  /** Local ID of the airtime record to claim */
  airtime_local_id: string;
}

/**
 * Claim airtime credits for a given utility item.
 * 
 * @param {ClaimData} data - The airtime ID to claim
 * @param {string} token - Bearer token for authorization
 * @returns {Promise<any>} Response data from the API
 */
export const ClaimAirtimes = async (data: ClaimData, token: string) => {
  const response = await axios.post(CLAIM_AIRTIME, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Fetch active airtime utilities with pagination.
 *
 * @param {HistoryData} data - Pagination parameters
 * @param {string} token - Bearer token for authorization
 * @returns {Promise<any>} Response data containing active utilities
 */
export const getActiveUtilities = async (data: HistoryData, token: string) => {
  const response = await axios.get(AIRTIME_ACTIVE_UTILITIES, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      perPage: data.per_page,
    },
  });
  return response.data;
};

/**
 * Fetch airtime transaction history with pagination.
 *
 * @param {HistoryData} data - Pagination parameters
 * @param {string} token - Bearer token for authorization
 * @returns {Promise<any>} Response data containing transaction history
 */
export const getAirTimeTransactions = async (data: HistoryData, token: string) => {
  const response = await axios.get(AIRTIME_TRANSACTIONS, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      per_Page: data.per_page,
    },
  });
  return response.data;
};