/* Critical Above-the-fold CSS */
:root {
  background-color: #1a1b1e;
  color: #ffffff;
}

body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: #1a1b1e;
  color: #ffffff;
}

/* Critical Layout Styles */
.min-h-screen {
  min-height: 100vh;
}

.bg-black {
  background-color: #000000;
}

.text-white {
  color: #ffffff;
}

/* Loading Spinner for Critical Path */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #000000;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 2px solid transparent;
  border-bottom: 2px solid #ED0CFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}