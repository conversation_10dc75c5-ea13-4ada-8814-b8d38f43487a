import React, { useState } from 'react';
import { Phone, MessageSquare, Play, Zap, Gamepad2, ShoppingBag, Train, Gift, Coins, BanknoteIcon, Shirt as <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Coffee, Headphones, X, ChevronRight } from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import useFetch from '../hooks/useFetch';
import { GET_USER_COINS } from '../api/auth';

interface ShopProps {
  onClose: () => void;
  balance: {
    coins: number;
    fpp: number;
  };
}

const DIGITAL_ITEMS = [
  {
    id: 'mobile_data',
    title: 'Mobile Data',
    icon: Phone,
    items: [
      { fpp: 500, value: '1GB', description: '30 days validity' },
      { fpp: 1000, value: '2GB', description: '30 days validity' },
      { fpp: 2000, value: '5GB', description: '30 days validity' }
    ]
  },
  {
    id: 'voice_calls',
    title: 'Voice Calls',
    icon: Phone,
    items: [
      { fpp: 300, value: '30 mins', description: 'All networks' },
      { fpp: 500, value: '60 mins', description: 'All networks' },
      { fpp: 1000, value: '120 mins', description: 'All networks' }
    ]
  },
  {
    id: 'sms',
    title: 'SMS Bundles',
    icon: MessageSquare,
    items: [
      { fpp: 100, value: '100 SMS', description: 'All networks' },
      { fpp: 200, value: '200 SMS', description: 'All networks' },
      { fpp: 500, value: '500 SMS', description: 'All networks' }
    ]
  },
  {
    id: 'streaming',
    title: 'Digital Content',
    icon: Play,
    items: [
      { fpp: 2000, value: 'Netflix Basic', description: '1 Month subscription' },
      { fpp: 3000, value: 'Spotify Premium', description: '1 Month subscription' },
      { fpp: 1500, value: 'News Premium', description: '1 Month subscription' }
    ]
  },
  {
    id: 'utilities',
    title: 'Utility Bills',
    icon: Zap,
    items: [
      { fpp: 1000, value: '₦1000 Electricity', description: 'Instant top-up' },
      { fpp: 2000, value: '₦2000 Electricity', description: 'Instant top-up' },
      { fpp: 1500, value: '₦1500 Water Bill', description: 'Bill payment' }
    ]
  },
  {
    id: 'gaming',
    title: 'Gaming Credits',
    icon: Gamepad2,
    items: [
      { fpp: 1000, value: '1000 PUBG UC', description: 'Instant delivery' },
      { fpp: 2000, value: '2000 FreeFire Diamonds', description: 'Instant delivery' },
      { fpp: 1500, value: '1500 V-Bucks', description: 'Instant delivery' }
    ]
  }
];

const MERCHANDISE = [
  {
    id: 'apparel',
    title: 'Apparel',
    icon: TShirt,
    items: [
      { 
        fpp: 5000, 
        value: 'Playzuzu T-Shirt', 
        description: 'Premium cotton, multiple sizes',
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop'
      },
      { 
        fpp: 8000, 
        value: 'Playzuzu Hoodie', 
        description: 'Cozy winter wear, multiple sizes',
        image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=300&h=200&fit=crop'
      },
      { 
        fpp: 3000, 
        value: 'Playzuzu Cap', 
        description: 'Adjustable size, premium quality',
        image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=300&h=200&fit=crop'
      }
    ]
  },
  {
    id: 'accessories',
    title: 'Accessories',
    icon: Watch,
    items: [
      { 
        fpp: 10000, 
        value: 'Smart Watch', 
        description: 'Fitness tracking, notifications',
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=200&fit=crop'
      },
      { 
        fpp: 15000, 
        value: 'Gaming Headset', 
        description: '7.1 surround sound, RGB',
        image: 'https://images.unsplash.com/photo-1599669454699-248893623440?w=300&h=200&fit=crop'
      },
      { 
        fpp: 5000, 
        value: 'Phone Case', 
        description: 'Premium protection, multiple models',
        image: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=300&h=200&fit=crop'
      }
    ]
  },
  {
    id: 'collectibles',
    title: 'Collectibles',
    icon: Gift,
    items: [
      { 
        fpp: 20000, 
        value: 'Limited Edition Figure', 
        description: 'Numbered collectible',
        image: 'https://images.unsplash.com/photo-1513384312027-9fa69a360337?w=300&h=200&fit=crop'
      },
      { 
        fpp: 12000, 
        value: 'Art Print', 
        description: 'Signed, limited series',
        image: 'https://images.unsplash.com/photo-1577083552431-6e5fd01aa342?w=300&h=200&fit=crop'
      },
      { 
        fpp: 8000, 
        value: 'Enamel Pin Set', 
        description: 'Collector\'s edition',
        image: 'https://images.unsplash.com/photo-1563592216771-c87226778e93?w=300&h=200&fit=crop'
      }
    ]
  },
  {
    id: 'lifestyle',
    title: 'Lifestyle',
    icon: Coffee,
    items: [
      { 
        fpp: 6000, 
        value: 'Premium Mug', 
        description: 'Ceramic, dishwasher safe',
        image: 'https://images.unsplash.com/photo-1514228742587-6b1558fcca3d?w=300&h=200&fit=crop'
      },
      { 
        fpp: 4000, 
        value: 'Notebook Set', 
        description: 'Premium paper, branded design',
        image: 'https://images.unsplash.com/photo-1531346680769-a1d79b57de5c?w=300&h=200&fit=crop'
      },
      { 
        fpp: 7000, 
        value: 'Backpack', 
        description: 'Water-resistant, laptop compartment',
        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=200&fit=crop'
      }
    ]
  }
];

const Shop: React.FC<ShopProps> = ({ onClose, balance }) => {
  const [activeTab, setActiveTab] = useState<'digital' | 'merch'>('digital');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const {accessToken} = useAuth();

  const {data} = useFetch(GET_USER_COINS,{
    headers:{
      Authorization : `Bearer ${accessToken}`
    }
  });
  const points = data?.data?.point

  const handlePurchase = (item: any) => {
    setSelectedItem(item);
    setShowConfirmation(true);
  };

  const confirmPurchase = () => {
    // Here you would implement the actual purchase logic
    console.log(`Purchasing ${selectedItem.value} for ${selectedItem.fpp} FPP`);
    setShowConfirmation(false);
    setSelectedItem(null);
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl w-full max-w-lg max-h-[80vh] flex flex-col">
        <div className="p-6 border-b border-white/10 flex justify-between items-center sticky top-0 bg-purple-900 rounded-t-2xl z-10">
          <h2 className="text-xl font-bold text-white">Shop</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Balance Display */}
        <div className="p-4 bg-white/5">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-white/60">Your Balance</p>
              <p className="text-lg font-bold">{balance.fpp} USD</p>
            </div>
            <div>
              <p className="text-sm text-white/60">Points</p>
              <p className="text-lg font-bold">{points}</p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-white/10">
          <button
            onClick={() => setActiveTab('digital')}
            className={`flex-1 py-3 text-sm font-medium transition-colors
              ${activeTab === 'digital' 
                ? 'text-yellow-400 border-b-2 border-yellow-400' 
                : 'text-white/60 hover:text-white'}`}
          >
            Digital Items
          </button>
          <button
            onClick={() => setActiveTab('merch')}
            className={`flex-1 py-3 text-sm font-medium transition-colors
              ${activeTab === 'merch' 
                ? 'text-yellow-400 border-b-2 border-yellow-400' 
                : 'text-white/60 hover:text-white'}`}
          >
            Merchandise
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {/* Digital Items */}
          {activeTab === 'digital' && !selectedCategory && (
            <div className="p-6 grid gap-4">
              {DIGITAL_ITEMS.map(category => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className="w-full bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-colors flex items-center justify-between"
                  >
                    <div className="flex items-center gap-4">
                      <div className="bg-white/10 p-2 rounded-lg">
                        <IconComponent size={24} className="text-white/60" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium">{category.title}</p>
                        <p className="text-sm text-white/60">From {category.items[0].fpp} Points</p>
                      </div>
                    </div>
                    <ChevronRight size={20} className="text-white/40" />
                  </button>
                );
              })}
            </div>
          )}

          {/* Merchandise */}
          {activeTab === 'merch' && !selectedCategory && (
            <div className="p-6 grid gap-6">
              {MERCHANDISE.map(category => (
                <div key={category.id}>
                  <div className="flex items-center gap-2 mb-4">
                    <category.icon size={20} className="text-white/60" />
                    <h3 className="text-lg font-semibold">{category.title}</h3>
                  </div>
                  <div className="grid gap-4">
                    {category.items.map((item, index) => (
                      <div
                        key={index}
                        className="bg-white/5 rounded-xl overflow-hidden hover:bg-white/10 transition-colors"
                      >
                        <img
                          src={item.image}
                          alt={item.value}
                          className="w-full h-40 object-cover"
                        />
                        <div className="p-4">
                          <h4 className="font-medium mb-1">{item.value}</h4>
                          <p className="text-sm text-white/60 mb-3">{item.description}</p>
                          <div className="flex justify-between items-center">
                            <p className="font-medium text-yellow-400">{item.fpp} Points</p>
                            <button
                              onClick={() => handlePurchase(item)}
                              disabled={balance.fpp < item.fpp}
                              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                                balance.fpp >= item.fpp
                                  ? 'bg-yellow-500 text-black'
                                  : 'bg-white/10 text-white/40'
                              }`}
                            >
                              {balance.fpp >= item.fpp ? 'Purchase' : 'Insufficient Points'}
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Selected Digital Category */}
          {activeTab === 'digital' && selectedCategory && (
            <div className="p-6">
              <button
                onClick={() => setSelectedCategory(null)}
                className="mb-6 text-white/60 hover:text-white flex items-center gap-2"
              >
                <X size={20} />
                <span>Back to categories</span>
              </button>

              <div className="space-y-4">
                {DIGITAL_ITEMS.find(cat => cat.id === selectedCategory)?.items.map((item, index) => (
                  <div
                    key={index}
                    className="bg-white/5 p-4 rounded-xl"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">{item.value}</h4>
                        <p className="text-sm text-white/60">{item.description}</p>
                      </div>
                      <p className="font-medium text-yellow-400">{item.fpp} Points</p>
                    </div>
                    <button
                      onClick={() => handlePurchase(item)}
                      disabled={balance.fpp < item.fpp}
                      className={`w-full mt-3 py-2 rounded-lg text-sm font-medium ${
                        balance.fpp >= item.fpp
                          ? 'bg-yellow-500 text-black'
                          : 'bg-white/10 text-white/40'
                      }`}
                    >
                      {balance.fpp >= item.fpp ? 'Purchase' : 'Insufficient Points'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Purchase Confirmation Modal */}
        {showConfirmation && selectedItem && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 w-full max-w-sm">
              <h3 className="text-xl font-bold mb-4">Confirm Purchase</h3>
              <p className="mb-2">Are you sure you want to purchase:</p>
              <div className="bg-white/5 rounded-lg p-4 mb-6">
                <p className="font-medium">{selectedItem.value}</p>
                <p className="text-sm text-white/60">{selectedItem.description}</p>
                <p className="text-yellow-400 font-medium mt-2">{selectedItem.fpp} Points</p>
              </div>
              <div className="flex gap-4">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="flex-1 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmPurchase}
                  className="flex-1 py-2 rounded-lg bg-yellow-500 text-black hover:bg-yellow-400 transition-colors"
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Shop;