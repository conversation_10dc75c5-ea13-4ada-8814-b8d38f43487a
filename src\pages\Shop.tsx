import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Shirt as TShirt, <PERSON>, ChevronRight, ChevronLeft, CheckCircle, MapPin, Truck, Loader } from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import { ClaimAirtimes, getActiveUtilities } from '../api/airTimeService';
import { CustomToast } from '../utils/validations/customeToast';
import { useCoins } from '../hooks/useCoinsQuery';
import { Balance } from '../components/Layout';

type ShopPageProps = {
  balance: Balance;
  onBalanceChange: (arg: Balance) => void;
};

type ClaimAirtimeInput = {
  local_id: string;
};
interface ShippingInfo {
  fullName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
}

const MERCHANDISE = [
  {
    id: 'apparel',
    title: 'Apparel',
    icon: TShirt,
    items: [
      {
        fpp: 500,
        value: 'Playzuzu T-Shirt',
        description: 'Premium cotton, multiple sizes',
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop'
      },
      {
        fpp: 8000,
        value: 'Playzuzu Hoodie',
        description: 'Cozy winter wear, multiple sizes',
        image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=300&h=200&fit=crop'
      },
      {
        fpp: 3000,
        value: 'Playzuzu Cap',
        description: 'Adjustable size, premium quality',
        image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=300&h=200&fit=crop'
      }
    ]
  },
  {
    id: 'accessories',
    title: 'Accessories',
    icon: Watch,
    items: [
      {
        fpp: 10000,
        value: 'Smart Watch',
        description: 'Fitness tracking, notifications',
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=200&fit=crop'
      },
      {
        fpp: 15000,
        value: 'Gaming Headset',
        description: '7.1 surround sound, RGB',
        image: 'https://images.unsplash.com/photo-1599669454699-248893623440?w=300&h=200&fit=crop'
      },
      {
        fpp: 5000,
        value: 'Phone Case',
        description: 'Premium protection, multiple models',
        image: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=300&h=200&fit=crop'
      }
    ]
  }
];

const SIZES = ['S', 'M', 'L', 'XL', '2XL'];

const ShopPage: React.FC<ShopPageProps> = ({ balance, onBalanceChange }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'digital' | 'merch'>('digital');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [purchaseStep, setPurchaseStep] = useState<'details' | 'shipping' | 'confirm' | 'success'>('details');
  const [selectedSize, setSelectedSize] = useState<string>('M');
  const [loadingItemId, setLoadingItemId] = useState<string | null>(null);
  const { accessToken } = useAuth();
  const [showUtilitySuccess, setShowUtilitySuccess] = useState(false);
  const [claimedUtilityName, setClaimedUtilityName] = useState('');

  const { coins, refetchCoins, isLoading: isLoadingCoins } = useCoins();

  const points = coins;


  const [shippingInfo, setShippingInfo] = useState<ShippingInfo>({
    fullName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    phone: '',
  });

  const CliamAirTime = useMutation<unknown, Error, ClaimAirtimeInput>({
    mutationFn: async (data) => {
      if (!accessToken) throw new Error("Access token missing");
      return ClaimAirtimes({ airtime_local_id: data.local_id }, accessToken);
    },
    onSuccess: ( variables:any) => {
      const claimedItem = utilities?.pages
        .flatMap(page => page.data?.airtime_utilities || [])
        .find(item => item.local_id === variables.local_id);
  
      if (claimedItem) {
        setClaimedUtilityName(claimedItem.billerName);
      }
  
      setShowUtilitySuccess(true);
      setLoadingItemId(null);
      refetchCoins();
  
      setTimeout(() => {
        setShowUtilitySuccess(false);
      }, 3000);
    },
    onError: (error: any) => {
      setLoadingItemId(null);
      CustomToast('error', error.message);
    },
  });

  const {
    data: utilities,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingUtilities,
  } = useInfiniteQuery({
    queryKey: ['utilities'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error("Access token missing");
      return getActiveUtilities({ page: pageParam, per_page: 10 }, accessToken)
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage ||
        !lastPage.data ||
        !lastPage.data.total_pages ||
        allPages.length >= lastPage.data.total_pages) {
        return undefined;
      }
      return allPages.length + 1; // Next page number
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
  });

  const handleClaimUtility = (item:any) => {
    setLoadingItemId(item.local_id);
    CliamAirTime.mutate({ local_id: item.local_id });
  };

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const handlePurchase = (item: any) => {
    setSelectedItem(item);
    setShowConfirmation(true);
    setPurchaseStep(item.image ? 'details' : 'confirm'); // If item has image, it's merchandise
  };

  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setPurchaseStep('confirm');
  };

  const confirmPurchase = () => {
    if (selectedItem && onBalanceChange && balance.fpp >= selectedItem.fpp) {
      onBalanceChange({
        ...balance,
        fpp: balance.fpp - selectedItem.fpp
      });
      setPurchaseStep('success');

      // Auto close after success
      setTimeout(() => {
        setShowConfirmation(false);
        setSelectedItem(null);
        setPurchaseStep('details');
        setShippingInfo({
          fullName: '',
          address: '',
          city: '',
          state: '',
          zipCode: '',
          phone: '',
        });
      }, 3000);
    }
  };

  // const LoadingSpinner = () => (
  //   <div className="flex justify-center items-center py-8">
  //     <Loader size={24} className="text-yellow-400 animate-spin" />
  //   </div>
  // );

  const flattenedUtilities = utilities?.pages.flatMap(page => page.data?.airtime_utilities || []) || [];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/')}
          className="text-white/60 hover:text-white transition-colors"
        >
          <ChevronLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">Shop</h1>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-white/10 mb-6">
        <button
          onClick={() => setActiveTab('digital')}
          className={`flex-1 py-3 text-sm font-medium transition-colors
            ${activeTab === 'digital'
              ? 'text-yellow-400 border-b-2 border-yellow-400'
              : 'text-white/60 hover:text-white'}`}
        >
          Digital Items
        </button>
        <button
          onClick={() => setActiveTab('merch')}
          className={`flex-1 py-3 text-sm font-medium transition-colors
            ${activeTab === 'merch'
              ? 'text-yellow-400 border-b-2 border-yellow-400'
              : 'text-white/60 hover:text-white'}`}
        >
          Merchandise
        </button>
      </div>

      {/* Balance Display */}
      <div className="bg-white/5 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-white/60">Your Balance</p>
            {isLoadingCoins ? (
              <div className="h-6 w-20 bg-white/10 rounded animate-pulse"></div>
            ) : (
              <p className="text-lg font-bold">{parseFloat(balance.real_money).toFixed(0)} USD</p>
            )}
          </div>
          <div>
            <p className="text-sm text-white/60">Bucks</p>
            {isLoadingCoins ? (
              <div className="h-6 w-20 bg-white/10 rounded animate-pulse"></div>
            ) : (
              <p className="text-lg font-bold">{points}</p>
            )}
          </div>
        </div>
      </div>

      {/* Digital Items */}
      {activeTab === 'digital'  && (
        <div className="grid gap-4">
          {isLoadingUtilities ? (
            // Loading skeleton
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="w-full bg-white/5 p-4 rounded-xl animate-pulse">
                <div className="flex items-center gap-4">
                  <div className="h-10 w-10 rounded-full bg-white/10"></div>
                  <div>
                    <div className="h-4 w-40 bg-white/10 rounded mb-2"></div>
                    <div className="h-3 w-24 bg-white/10 rounded"></div>
                  </div>
                </div>
              </div>
            ))
          ) : flattenedUtilities.length === 0 ? (
            <div className="text-center py-8 text-white/60">
              No digital items available at the moment
            </div>
          ) : (
            <>
              {flattenedUtilities.map(item => (
                <button
                  key={item.id}
                  onClick={() => handleClaimUtility(item)}
                  disabled={loadingItemId === item.local_id}
                  className="w-full bg-white/5 p-4 rounded-xl hover:bg-white/10 transition-colors flex items-center justify-between"
                >
                  <div className="flex items-center gap-4">
                    <div className="text-left">
                      <p className="font-medium">{item?.billerName}</p>
                      <p className="text-sm text-white/60">From {item?.price} Bucks</p>
                    </div>
                  </div>
                  {loadingItemId === item.local_id ? (
                    <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
                  ) : (
                    <ChevronRight size={20} className="text-white/40" />
                  )}
                </button>
              ))}

              {hasNextPage && (
                <button
                  onClick={handleLoadMore}
                  className="w-full py-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors mt-4"
                  disabled={isFetchingNextPage}
                >
                  {isFetchingNextPage ? (
                    <div className="flex justify-center items-center">
                      <Loader size={20} className="animate-spin text-white/60" />
                    </div>
                  ) : (
                    "Load More"
                  )}
                </button>
              )}
            </>
          )}
        </div>
      )}

      {/* Merchandise */}
      {activeTab === 'merch' && (
        <div className="space-y-8">
          {MERCHANDISE.map(category => (
            <div key={category.id}>
              <div className="flex items-center gap-2 mb-4">
                <category.icon size={20} className="text-white/60" />
                <h3 className="text-lg font-semibold">{category.title}</h3>
              </div>
              <div className="grid gap-4">
                {category.items.map((item, index) => (
                  <div
                    key={index}
                    className="bg-white/5 rounded-xl overflow-hidden hover:bg-white/10 transition-colors"
                  >
                    <img
                      src={item.image}
                      alt={item.value}
                      className="w-full h-40 object-cover"
                    />
                    <div className="p-4">
                      <h4 className="font-medium mb-1">{item.value}</h4>
                      <p className="text-sm text-white/60 mb-3">{item.description}</p>
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-yellow-400">{item.fpp} Bucks</p>
                        <button
                          onClick={() => handlePurchase(item)}
                          disabled={balance.fpp < item.fpp}
                          className={`px-4 py-2 rounded-lg text-sm font-medium ${balance.fpp >= item.fpp
                            ? 'bg-yellow-500 text-black hover:bg-yellow-400'
                            : 'bg-white/10 text-white/40'
                            }`}
                        >
                          {balance.fpp >= item.fpp ? 'Purchase' : 'Insufficient Bucks'}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Utility Purchase Success Modal */}
      {showUtilitySuccess && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 w-full max-w-lg">
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-2">Purchase Successful!</h3>
              <p className="text-white/60">
                Your {claimedUtilityName} has been claimed successfully.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Purchase Flow Modals */}
      {showConfirmation && selectedItem && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 w-full max-w-lg">
            {/* Progress Steps - Only show for merchandise */}
            {selectedItem.image && (
              <div className="flex items-center justify-between mb-6">
                {['details', 'shipping', 'confirm'].map((step, index) => (
                  <React.Fragment key={step}>
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${purchaseStep === step
                        ? 'bg-yellow-500 text-black'
                        : purchaseStep === 'success' || ['details', 'shipping', 'confirm'].indexOf(purchaseStep) > ['details', 'shipping', 'confirm'].indexOf(step)
                          ? 'bg-green-500 text-white'
                          : 'bg-white/10 text-white/60'
                        }`}>
                        {purchaseStep === 'success' || ['details', 'shipping', 'confirm'].indexOf(purchaseStep) > ['details', 'shipping', 'confirm'].indexOf(step)
                          ? <CheckCircle size={16} />
                          : index + 1
                        }
                      </div>
                      <span className="ml-2 text-sm font-medium capitalize">{step}</span>
                    </div>
                    {index < 2 && (
                      <div className="flex-1 mx-4 h-0.5 bg-white/10" />
                    )}
                  </React.Fragment>
                ))}
              </div>
            )}

            {/* Product Details Step */}
            {purchaseStep === 'details' && selectedItem.image && (
              <>
                <h3 className="text-xl font-bold mb-4">Product Details</h3>
                <div className="bg-white/5 rounded-lg overflow-hidden mb-6">
                  <img
                    src={selectedItem.image}
                    alt={selectedItem.value}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-4">
                    <h4 className="font-medium text-lg mb-2">{selectedItem.value}</h4>
                    <p className="text-white/60 mb-4">{selectedItem.description}</p>
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-2">Select Size</label>
                      <div className="flex gap-2">
                        {SIZES.map(size => (
                          <button
                            key={size}
                            onClick={() => setSelectedSize(size)}
                            className={`w-12 h-12 rounded-lg font-medium transition-colors ${selectedSize === size
                              ? 'bg-yellow-500 text-black'
                              : 'bg-white/10 text-white hover:bg-white/20'
                              }`}
                          >
                            {size}
                          </button>
                        ))}
                      </div>
                    </div>
                    <div className="flex justify-between items-center text-lg font-medium">
                      <span>Price:</span>
                      <span className="text-yellow-400">{selectedItem.fpp} Bucks</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={() => setShowConfirmation(false)}
                    className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => setPurchaseStep('shipping')}
                    className="flex-1 py-3 rounded-lg bg-yellow-500 text-black hover:bg-yellow-400 transition-colors"
                  >
                    Continue to Shipping
                  </button>
                </div>
              </>
            )}

            {/* Shipping Information Step */}
            {purchaseStep === 'shipping' && (
              <>
                <h3 className="text-xl font-bold mb-4">Shipping Information</h3>
                <form onSubmit={handleShippingSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Full Name</label>
                    <input
                      type="text"
                      required
                      value={shippingInfo.fullName}
                      onChange={(e) => setShippingInfo({ ...shippingInfo, fullName: e.target.value })}
                      className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Address</label>
                    <input
                      type="text"
                      required
                      value={shippingInfo.address}
                      onChange={(e) => setShippingInfo({ ...shippingInfo, address: e.target.value })}
                      className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                      placeholder="Enter your address"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">City</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.city}
                        onChange={(e) => setShippingInfo({ ...shippingInfo, city: e.target.value })}
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                        placeholder="City"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">State</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.state}
                        onChange={(e) => setShippingInfo({ ...shippingInfo, state: e.target.value })}
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                        placeholder="State"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">ZIP Code</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.zipCode}
                        onChange={(e) => setShippingInfo({ ...shippingInfo, zipCode: e.target.value })}
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                        placeholder="ZIP Code"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Phone</label>
                      <input
                        type="tel"
                        required
                        value={shippingInfo.phone}
                        onChange={(e) => setShippingInfo({ ...shippingInfo, phone: e.target.value })}
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 focus:border-yellow-500 focus:outline-none"
                        placeholder="Phone number"
                      />
                    </div>
                  </div>
                  <div className="flex gap-4 mt-6">
                    <button
                      type="button"
                      onClick={() => setPurchaseStep('details')}
                      className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                    >
                      Back
                    </button>
                    <button
                      type="submit"
                      className="flex-1 py-3 rounded-lg bg-yellow-500 text-black hover:bg-yellow-400 transition-colors"
                    >
                      Continue to Confirmation
                    </button>
                  </div>
                </form>
              </>
            )}

            {/* Confirmation Step */}
            {purchaseStep === 'confirm' && (
              <>
                <h3 className="text-xl font-bold mb-4">Order Confirmation</h3>
                <div className="bg-white/5 rounded-lg p-4 mb-6">
                  <div className="flex items-start gap-4 mb-4">
                    {selectedItem.image && (
                      <img
                        src={selectedItem.image}
                        alt={selectedItem.value}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                    )}
                    <div>
                      <h4 className="font-medium">{selectedItem.value}</h4>
                      <p className="text-sm text-white/60">{selectedItem.description}</p>
                      {selectedSize && <p className="text-sm text-white/80 mt-1">Size: {selectedSize}</p>}
                    </div>
                  </div>

                  {selectedItem.image && (
                    <div className="border-t border-white/10 pt-4 mb-4">
                      <h5 className="font-medium mb-2 flex items-center gap-2">
                        <MapPin size={16} />
                        Shipping Address
                      </h5>
                      <p className="text-sm text-white/60">
                        {shippingInfo.fullName}<br />
                        {shippingInfo.address}<br />
                        {shippingInfo.city}, {shippingInfo.state} {shippingInfo.zipCode}<br />
                        Phone: {shippingInfo.phone}
                      </p>
                    </div>
                  )}

                  <div className="border-t border-white/10 pt-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-white/60">Price</span>
                      <span>{selectedItem.fpp} Bucks</span>
                    </div>
                    {selectedItem.image && (
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/60">Shipping</span>
                        <span>Free</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center text-lg font-medium pt-2 border-t border-white/10">
                      <span>Total</span>
                      <span className="text-yellow-400">{selectedItem.fpp} Bucks</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={() => setPurchaseStep(selectedItem.image ? 'shipping' : 'details')}
                    className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={confirmPurchase}
                    className="flex-1 py-3 rounded-lg bg-yellow-500 text-black hover:bg-yellow-400 transition-colors"
                  >
                    Confirm Purchase
                  </button>
                </div>
              </>
            )}

            {/* Success Step */}
            {purchaseStep === 'success' && (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle size={32} className="text-green-400" />
                </div>
                <h3 className="text-xl font-bold mb-2">Purchase Successful!</h3>
                {selectedItem.image ? (
                  <>
                    <p className="text-white/60 mb-4">
                      Your order has been placed and will be shipped to your address.
                    </p>
                    <div className="flex items-center justify-center gap-2 text-yellow-400">
                      <Truck size={20} />
                      <span>Estimated delivery: 3-5 business days</span>
                    </div>
                  </>
                ) : (
                  <p className="text-white/60">
                    Your digital item has been added to your account.
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ShopPage;