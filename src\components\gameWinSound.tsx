// e.g. src/components/GameWithSound.tsx
import React, { useEffect, useRef } from 'react';
import winSoundUrl from '../Sounds/Crypto King/Win Sound.m4a';

interface Props { playWin: boolean; }

const GameWithSound: React.FC<Props> = ({ playWin }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    // Create the Audio instance once on mount
    audioRef.current = new Audio(winSoundUrl);
    return () => {
      // Cleanup on unmount
      audioRef.current?.pause();
      audioRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (playWin && audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(console.error);
    }
  }, [playWin]);

  return null; // this component is purely for sound side-effects
};

export default GameWithSound;
