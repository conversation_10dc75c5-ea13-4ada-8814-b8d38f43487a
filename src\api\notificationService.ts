import axios from 'axios';
import { GET_NOTIFICATIONS, MARK_ALL_NOTIFICATIONS_READ } from './auth';



export interface params {
  page: number;
  per_page: number;
  // version: string;
}

/**
 * Get Notification
 */
export const getNotification = async (data:params,token: string) => {
  const response = await axios.get(GET_NOTIFICATIONS, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data?.page||1,
      per_page: data?.per_page||10,
    },
  });
  return response.data;
};


/**
 * Mark All Notification
 */
export const markAllReadNotification  = async (token: string) => {
  const response = await axios.patch(MARK_ALL_NOTIFICATIONS_READ,{},{
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};


/**
 * Mark Notification By Id
 */
export const markReadNotificationByID = async ( id:string,token: string) => {
  const response = await axios.patch(`${GET_NOTIFICATIONS}/${id}/mark-read`,{}, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

/**
 * Delete Notification By Id
 */
export const deletNotificationById = async ( id:string,token: string) => {
  const response = await axios.delete(`${GET_NOTIFICATIONS}/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};