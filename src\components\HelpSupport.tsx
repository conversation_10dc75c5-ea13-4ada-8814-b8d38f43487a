import React, { useState,useEffect } from 'react';
import { X, Search, ChevronRight, MessageCircle, Phone, Mail, Globe, ArrowLeft, HelpCircle, Shield, Wallet, Gift, Award, AlertCircle } from 'lucide-react';

interface HelpSupportProps {
  onClose: () => void;
}

interface FAQCategory {
  id: string;
  title: string;
  icon: React.ElementType;
  questions: {
    q: string;
    a: string;
  }[];
}

const FAQ_CATEGORIES: FAQCategory[] = [
  {
    id: 'account',
    title: 'Account & Security',
    icon: Shield,
    questions: [
      {
        q: 'How do I reset my password?',
        a: 'To reset your password, go to the login page and click on "Forgot Password". Follow the instructions sent to your email to create a new password.'
      },
      {
        q: 'How do I enable two-factor authentication?',
        a: 'You can enable 2FA in Settings > Security > Two-Factor Authentication. Choose between authenticator app or SMS verification.'
      },
      {
        q: 'What should I do if I suspect unauthorized access?',
        a: 'Immediately change your password and contact our support team. Review your account activity and enable two-factor authentication for additional security.'
      }
    ]
  },
  {
    id: 'wallet',
    title: 'Wallet & Transactions',
    icon: Wallet,
    questions: [
      {
        q: 'How do I add funds to my wallet?',
        a: 'You can earn coins by playing games, watching ads, or participating in special events. FPP points are earned through achievements and daily rewards.'
      },
      {
        q: 'What\'s the difference between Coins and FPP?',
        a: 'Coins are used for playing games and betting, while FPP (Playzuzu Points) can be converted into various rewards like airtime, data, and merchandise.'
      },
      {
        q: 'Are my transactions secure?',
        a: 'Yes, all transactions are encrypted and processed through secure channels. We use industry-standard security protocols to protect your data.'
      }
    ]
  },
  {
    id: 'rewards',
    title: 'Games & Rewards',
    icon: Gift,
    questions: [
      {
        q: 'How do I claim my rewards?',
        a: 'Navigate to the Rewards section to view and claim your available rewards. Some rewards may have specific requirements or waiting periods.'
      },
      {
        q: 'What are the different game modes?',
        a: 'We offer various game modes including Quick Hustle, Street King, and special event games. Each mode has different stakes and reward multipliers.'
      },
      {
        q: 'How do I level up faster?',
        a: 'Play regularly, complete achievements, watch reward videos, and participate in events to earn Bucks and level up quickly.'
      }
    ]
  },
  {
    id: 'technical',
    title: 'Technical Support',
    icon: HelpCircle,
    questions: [
      {
        q: 'What devices are supported?',
        a: 'Playzuzu works on most modern web browsers on both desktop and mobile devices. For the best experience, use Chrome, Safari, or Firefox.'
      },
      {
        q: 'How do I report a bug?',
        a: 'Use the "Report Issue" button in Settings or contact our support team with details about the bug, including screenshots if possible.'
      },
      {
        q: 'Why is the game not loading?',
        a: 'Try clearing your browser cache, checking your internet connection, or refreshing the page. If issues persist, contact our support team.'
      }
    ]
  }
];

const HelpSupport: React.FC<HelpSupportProps> = ({ onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<FAQCategory | null>(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };



    // Check if mobile on mount and resize
    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };
  
      checkMobile();
      window.addEventListener('resize', checkMobile);
  
      // Trigger animation
      setTimeout(() => setIsAnimating(true), 50);
  
      return () => window.removeEventListener('resize', checkMobile);
    }, []);
  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    setShowContactForm(false);
    // Show success message or handle errors
  };

  const filteredCategories = searchQuery
    ? FAQ_CATEGORIES.filter(category =>
        category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.questions.some(q =>
          q.q.toLowerCase().includes(searchQuery.toLowerCase()) ||
          q.a.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    : FAQ_CATEGORIES;


      // Mobile bottom sheet styles
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;
  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
          <div className="flex items-center gap-3">
            {(selectedCategory || showContactForm) && (
              <button
                onClick={() => {
                  setSelectedCategory(null);
                  setShowContactForm(false);
                }}
                className="text-white/60 hover:text-white transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
            )}
            <h2 className="text-xl  text-white font-[Anton] tracking-wide">Help & Support</h2>
          </div>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {!selectedCategory && !showContactForm && (
            <div className="p-6">
              {/* Search */}
              <div className="relative mb-6">
                <input
                  type="text"
                  placeholder="Search help articles..."
                  value={searchQuery}
                  onChange={handleSearch}
                  className="w-full bg-white/10 border border-white/20 rounded-xl pl-12 pr-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                />
                <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-white/40" />
              </div>

              {/* Contact Support Button */}
              <div className="mb-8">
                <button
                  onClick={() => setShowContactForm(true)}
                  className="w-full bg-[#131313] p-4 rounded-xl  transition-colors text-left"
                >
                  <MessageCircle size={24} className="text-[#ED0CFF] mb-2" />
                  <p className="text-[16px] font-[Anton] tracking-wide">Contact Support</p>
                  <p className="text-sm text-white/60">Get help from our team</p>
                </button>
              </div>

              {/* FAQ Categories */}
              <h3 className="text-lg  text-white font-[Anton] tracking-wide mb-4 ">Popular Topics</h3>
              <div className="space-y-3">
                {filteredCategories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category)}
                    className="w-full bg-[#131313] p-4 rounded-xl  transition-colors flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-white/10 p-2 rounded-lg">
                        <category.icon size={20} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="text-[16px] font-[Anton] tracking-wide ">{category.title}</p>
                        <p className="text-[12px] text-white/60">{category.questions.length} articles</p>
                      </div>
                    </div>
                    <ChevronRight size={20} className="text-white" />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Selected Category View */}
          {selectedCategory && (
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-6 flex items-center gap-2 font-[Anton] tracking-wide ">
                 <div className="bg-[#1c1c1c] p-2 rounded-lg">
                <selectedCategory.icon size={20} className="text-[#ED0CFF]" />
                </div>
                {selectedCategory.title}
              </h3>
              
              <div className="space-y-4">
                {selectedCategory.questions.map((item, index) => (
                  <details
                    key={index}
                    className="group bg-white/5 rounded-xl overflow-hidden"
                  >
                    <summary className="flex items-center justify-between p-4 cursor-pointer hover:bg-white/10 transition-colors">
                      <span className="font-medium">{item.q}</span>
                      <ChevronRight size={20} className="text-white transition-transform group-open:rotate-90" />
                    </summary>
                    <div className="p-4 pt-0 text-white/80 text-sm leading-relaxed">
                      {item.a}
                    </div>
                  </details>
                ))}
              </div>
            </div>
          )}

          {/* Contact Form */}
          {showContactForm && (
            <div className="p-6">
              <div className="bg-white/5 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-[#1c1c1c] p-2 rounded-lg">
                    <MessageCircle size={20} className="text-[#ED0CFF]" />
                  </div>
                  <p className="text-sm text-white/60">
                    Our support team typically responds within 24 hours.
                  </p>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail size={16} className="text-white/40" />
                    <span>Email Support</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone size={16} className="text-white/40" />
                    <span>24/7 Phone</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe size={16} className="text-white/40" />
                    <span>Live Chat</span>
                  </div>
                </div>
              </div>

              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-normal font-[Poppins] mb-1">Name</label>
                  <input
                    type="text"
                    value={contactForm.name}
                    onChange={(e) => setContactForm({ ...contactForm, name: e.target.value })}
                    className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
                    placeholder="Your name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-normal font-[Poppins] mb-1">Email</label>
                  <input
                    type="email"
                    value={contactForm.email}
                    onChange={(e) => setContactForm({ ...contactForm, email: e.target.value })}
                    className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
                    placeholder="Your email address"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-normal font-[Poppins] mb-1">Subject</label>
                  <input
                    type="text"
                    value={contactForm.subject}
                    onChange={(e) => setContactForm({ ...contactForm, subject: e.target.value })}
                    className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
                    placeholder="What\'s this about?"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-normal font-[Poppins] mb-1">Message</label>
                  <textarea
                    value={contactForm.message}
                    onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                    className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] min-h-[120px]"
                    placeholder="Describe your issue or question"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="w-full py-3 bg-white text-black rounded-lg font-medium  transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HelpSupport;