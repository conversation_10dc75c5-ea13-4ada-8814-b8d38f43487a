import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Trophy, Medal, Crown, Star, Search, Filter, TrendingUp, Clock } from 'lucide-react';

interface LeaderboardEntry {
  id: string;
  rank: number;
  name: string;
  username: string;
  avatar: string;
  score: number;
  winRate: number;
  level: number;
  isCurrentUser?: boolean;
}

const MOCK_LEADERBOARD: LeaderboardEntry[] = [
  {
    id: '1',
    rank: 1,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    username: 'seun',
    avatar: 'https://images.unsplash.com/photo-1531384441138-2736e62e0919?w=100&h=100&fit=crop',
    score: 250000,
    winRate: 78,
    level: 25,
    isCurrentUser: true
  },
  {
    id: '2',
    rank: 2,
    name: '<PERSON><PERSON>',
    username: 'chioma_wins',
    avatar: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=100&h=100&fit=crop',
    score: 245000,
    winRate: 75,
    level: 24
  },
  {
    id: '3',
    rank: 3,
    name: '<PERSON><PERSON><PERSON>',
    username: 'bj_hustle',
    avatar: 'https://images.unsplash.com/photo-1522529599102-193c0d76b5b6?w=100&h=100&fit=crop',
    score: 240000,
    winRate: 72,
    level: 23
  },
  {
    id: '4',
    rank: 4,
    name: 'Aisha Mohammed',
    username: 'aisha_m',
    avatar: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=100&h=100&fit=crop',
    score: 235000,
    winRate: 70,
    level: 22
  },
  {
    id: '5',
    rank: 5,
    name: 'Emeka Eze',
    username: 'emeka_king',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop',
    score: 230000,
    winRate: 68,
    level: 21
  }
];

const LEADERBOARD_TABS = [
  { id: 'all-time', label: 'All Time', icon: Trophy },
  { id: 'weekly', label: 'This Week', icon: TrendingUp },
  { id: 'daily', label: 'Today', icon: Clock }
];

const LeaderboardPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all-time');
  const [searchQuery, setSearchQuery] = useState('');

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown size={24} className="text-yellow-400" />;
      case 2:
        return <Medal size={24} className="text-gray-400" />;
      case 3:
        return <Medal size={24} className="text-orange-400" />;
      default:
        return <span className="text-lg font-bold text-white/60">{rank}</span>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/')}
          className="text-white/60 hover:text-white transition-colors"
        >
          <ChevronLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">Leaderboard</h1>
      </div>

      {/* Search and Filter */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search players..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-white/5 border border-white/10 rounded-xl pl-12 pr-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
          />
          <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-white/40" />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
        {LEADERBOARD_TABS.map(tab => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-yellow-500 text-black'
                  : 'bg-white/5 text-white hover:bg-white/10'
              }`}
            >
              <IconComponent size={16} />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Top 3 Podium */}
      <div className="grid grid-cols-3 gap-4 mb-8">
        {MOCK_LEADERBOARD.slice(0, 3).map((entry, index) => (
          <div
            key={entry.id}
            className={`relative bg-gradient-to-b from-white/5 to-white/10 rounded-xl p-4 text-center transform transition-transform hover:scale-105 ${
              index === 0 ? 'order-2' : index === 1 ? 'order-1' : 'order-3'
            }`}
          >
            <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2">
              {getRankIcon(entry.rank)}
            </div>
            <img
              src={entry.avatar}
              alt={entry.name}
              className={`w-16 h-16 mx-auto rounded-full border-2 mb-3 ${
                entry.rank === 1
                  ? 'border-yellow-400'
                  : entry.rank === 2
                  ? 'border-gray-400'
                  : 'border-orange-400'
              }`}
            />
            <h3 className="font-medium mb-1">{entry.name}</h3>
            <p className="text-sm text-white/60 mb-2">@{entry.username}</p>
            <div className="flex items-center justify-center gap-2 text-sm">
              <Star size={14} className="text-yellow-400" />
              <span>Level {entry.level}</span>
            </div>
            <p className="text-lg font-bold mt-2">{entry.score.toLocaleString()} pts</p>
          </div>
        ))}
      </div>

      {/* Leaderboard List */}
      <div className="space-y-2">
        {MOCK_LEADERBOARD.slice(3).map(entry => (
          <div
            key={entry.id}
            className={`flex items-center gap-4 p-4 rounded-xl transition-colors ${
              entry.isCurrentUser
                ? 'bg-yellow-500/20 border border-yellow-500/20'
                : 'bg-white/5 hover:bg-white/10'
            }`}
          >
            <div className="w-8 text-center">{entry.rank}</div>
            <img
              src={entry.avatar}
              alt={entry.name}
              className="w-12 h-12 rounded-full"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-medium">{entry.name}</h3>
                {entry.isCurrentUser && (
                  <span className="text-xs bg-yellow-500 text-black px-2 py-0.5 rounded-full">
                    You
                  </span>
                )}
              </div>
              <p className="text-sm text-white/60">@{entry.username}</p>
            </div>
            <div className="text-right">
              <p className="font-bold">{entry.score.toLocaleString()} pts</p>
              <p className="text-sm text-white/60">Win Rate: {entry.winRate}%</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LeaderboardPage;