// ConfirmModal.tsx
import React from 'react';
import { X } from 'lucide-react';

interface ConfirmModalProps {
  visible: boolean;
  title?: string;
  description?: string;
  onCancel: () => void;
  onConfirm: () => void;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  title = 'Confirm Delete',
  description = 'Are you sure you want to remove this member?',
  onCancel,
  onConfirm,
}) => {
  if (!visible) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="  bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] text-white w-full max-w-sm rounded-xl shadow-lg p-6 relative">
        <button
          className="absolute top-3 right-3 text-white hover:text-gray-400"
          onClick={onCancel}
        >
          <X size={20} />
        </button>
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        <p className="text-sm text-white/70 mb-6">{description}</p>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 rounded-md bg-white/10 hover:bg-white/20 transition"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white transition"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmModal;
