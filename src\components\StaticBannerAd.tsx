import React from 'react';

const StaticBannerAd = () => {
  return (
    <div className="w-[320px] h-[100px] mx-auto bg-white rounded shadow-lg overflow-hidden">
      <div className="flex h-full">
        {/* Ad Image */}
        <div className="w-[100px] h-full flex-shrink-0">
          <img
            src="https://images.unsplash.com/photo-1612547036242-77002603e5aa?w=100&h=100&fit=crop"
            alt="Ad"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Ad Content */}
        <div className="flex-1 p-3">
          <div className="text-[10px] text-blue-600 font-semibold mb-1">Advertisement</div>
          <h3 className="text-sm font-bold text-gray-900 mb-1 line-clamp-1">
            Play & Win Big! 🎮
          </h3>
          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
            Join millions of players worldwide. Start with 5000 free coins!
          </p>
          <button
            onClick={() => window.open('https://example.com', '_blank')}
            className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded hover:bg-green-600 transition-colors"
          >
            PLAY NOW ▶
          </button>
        </div>
      </div>
    </div>
  );
};

export default StaticBannerAd;