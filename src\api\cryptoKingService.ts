import axios from 'axios';
import {
GET_CRYPTO_PRICE,
CRYPTO_BET_PLACE
} from './auth';

export interface HistoryData {
  page: number;
  per_page: number;
  // version: string;
}
interface BetData {
  bet_amount: number,
  max_value: number,
  min_value: number,
  second: number,
  type: string
}

export const PlaceCryptoBet = async (data: BetData, token: string) => {
  const response = await axios.post(CRYPTO_BET_PLACE, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};
export const getTradeHistory = async (data: HistoryData, token: string) => {
  const response = await axios.get(CRYPTO_BET_PLACE, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params: {
      page: data.page,
      perPage: data.per_page,
    },
  });
  return response.data;
};
export const getCryptoPrice = async (token: string) => {
    const response = await axios.get(GET_CRYPTO_PRICE, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });
  
    return response.data;
  };