import { useEffect, useState } from 'react';
import { IMAGES } from '../constant/image';
import { preloadImage } from '../utils/helper';

/**
 * Get isGameDataLoading boolean state
 * @param loadingMarkers - whether all arguments are true it means data is fully loaded so the hook returns false
 * @returns { isGameDataLoading: boolean, visibility: 'hidden' | 'visible' } - visibility is used for components with refs as it doesn't work conditionally
 */

export const useGameDataLoading = (
  ...loadingMarkers: boolean[]
): { isGameDataLoading: boolean; visibility: 'hidden' | 'visible' } => {
  const [isGameDataLoading, setIsGameDataLoading] = useState(true);

  const visibility = isGameDataLoading ? 'hidden' : 'visible';

  useEffect(() => {
    !loadingMarkers.length &&
      [
        IMAGES.LOADING_BG_1,
        IMAGES.LOADING_BG_2,
        IMAGES.LOADING_BG_3,
        IMAGES.LOADING_BG_4,
        IMAGES.LOADING_BG_5,
        IMAGES.SPINNER_1,
        IMAGES.SPINNER_2,
        IMAGES.SPINNER_3,
        IMAGES.SPINNER_4,
        IMAGES.SPINNER_5,
      ].forEach(preloadImage);
  }, []);

  useEffect(() => {
    if (loadingMarkers.length && isGameDataLoading) {
      const isGameDataLoaded = loadingMarkers?.every(Boolean);
      isGameDataLoaded && setIsGameDataLoading(false);
    }
  }, [loadingMarkers, isGameDataLoading]);

  return { isGameDataLoading, visibility };
};
