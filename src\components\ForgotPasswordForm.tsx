import React, { useState } from 'react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { ChevronLeft } from 'lucide-react';

interface ForgotPasswordFormProps {
  onForgotPassword: (phoneNumber: string) => Promise<void>;
  onSwitchTab: () => void;
  error: string;
}

const phoneRegExp = /^[0-9]{10}$/;

const ForgotPasswordSchema = Yup.object().shape({
  phoneNumber: Yup.string()
    .trim()
    .required('Phone number is required')
    .matches(phoneRegExp, 'Please enter a valid 10-digit phone number')
});

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onForgotPassword,
  onSwitchTab,
  error,
}) => {
  const handleSubmit = (
    values: { phoneNumber: string },
    { setSubmitting }: FormikHelpers<{ phoneNumber: string }>
  ) => {
    onForgotPassword(values.phoneNumber);
    setSubmitting(false);
  };

  return (
    <div className="max-w-sm mx-auto">
      <div className="flex items-center gap-1.3 mb-4">
        <button
          type="button"
          onClick={() => {
            console.log('Back clicked');
            onSwitchTab();
          }}
          className="text-white"
        >
          <ChevronLeft className='font-bold' />
        </button>
        <h2 className="text-[16px] leading-[24px] font-bold tracking-[0.02rem] uppercase font-[Anton] text-white">
          FORGOT PASSWORD
        </h2>

      </div>

      <Formik
        initialValues={{ phoneNumber: '' }}
        validationSchema={ForgotPasswordSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched }) => (
          <Form>
            <label htmlFor="phoneNumber" className="block text-sm font-normal font-[Poppins] mb-1">
              Phone Number
            </label>
            <Field
              id="phoneNumber"
              name="phoneNumber"
              type="tel"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={10}
              placeholder="Enter your 10-digit phone number"
              className={`w-full bg-white/10 border ${errors.phoneNumber && touched.phoneNumber
                ? 'border-red-500'
                : 'border-white/20'
                } rounded-lg pl-2 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400`}
              onKeyPress={(e: React.KeyboardEvent) => {
                const isNumber = /[0-9]/.test(e.key);
                if (!isNumber && e.key !== 'Backspace' && e.key !== 'Tab') {
                  e.preventDefault();
                }
              }}
            />
            <ErrorMessage
              name="phoneNumber"
              component="div"
              className="text-red-500 mb-2 mt-2"
            />

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full p-2 mt-6 rounded font-normal flex text-white rounded-lg  tracking-wider
                   uppercase font-[Anton] justify-center items-center bg-[#ED0CFF]"
            >
              {isSubmitting ? 'Sending OTP...' : 'Send OTP'}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default ForgotPasswordForm;
