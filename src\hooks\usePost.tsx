// import { useMutation } from "@tanstack/react-query";
// import { useRouter } from "next/navigation";
// import { CustomToast } from "../Validations/CustomToast";
// import CustomFetch from "../APIRequest/AxiosInstance";

// const usePost = (
//   url: any,
//   path: any,
//   extraFunction: any,
//   method: any,
//   errorMessage: any = false,
//   headers: any = {}
// ) => {
//   const router = useRouter();
//   return useMutation({
//     mutationFn: (data) =>
//       CustomFetch(
//         { url, data, method: method ? method : "post", headers },
//         null,
        
//         method === "POST" ? true : false,
//         errorMessage
//       ),
//     onSuccess: (response) => {
//       if (
//         response?.status === 200 ||
//         response?.status === 201 ||
//         response?.status === 204 ||
//         response?.status === 400
//       ) {
//         extraFunction && extraFunction(response);
//         path && router.push(path);
//       } else {
//         CustomToast("error", response?.response?.data?.message);
//       }
//     },
//   });
// };

// export default usePost;
