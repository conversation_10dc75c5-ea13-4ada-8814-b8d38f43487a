import React from 'react';
import { useAuth } from '../auth/AuthContext';

const Lottery: React.FC = () => {
  const { accessToken } = useAuth();

  return (
    <iframe
      // src={`http://localhost:3000/?token=${encodeURIComponent(accessToken || '')}`}
            src={`https://lottery-v2-ashy.vercel.app/?token=${encodeURIComponent(accessToken || '')}`}
      title="Lottery"
      className="w-full h-full rounded-xl border-none"
      style={{ minHeight: '80vh', minWidth: '100%' }}
      allow="clipboard-write; encrypted-media;"
    />
  );
};

export default Lottery;
