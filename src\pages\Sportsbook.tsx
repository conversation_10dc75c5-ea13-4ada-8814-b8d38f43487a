import React, { useEffect } from "react";
import { useAuth } from "../auth/AuthContext";
import { ALTENAR_CONFIG } from "../config/altenarConfig";

declare global {
  interface Window {
    altenarWSDK?: any;
    ASB?: any;
    WTEC?: any;
    WBBR: any;
  }
}

const ALTENAR_JS_URL = ALTENAR_CONFIG.JS_URL;

const SportsbookPage: React.FC = () => {
  const { accessToken, isAuthenticated, userProfile } = useAuth();

  useEffect(() => {
    if (!window.altenarWSDK) {
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.src = ALTENAR_JS_URL;
      script.onload = () => initializeSportsbook();
      document.body.appendChild(script);
    } else {
      initializeSportsbook();
    }

    function initializeSportsbook() {
      function tryInit() {
        if (
          window.altenarWSDK &&
          document.getElementById("altenarsportsbook")
        ) {
          // Initialize Altenar with user authentication if available
          const initConfig: any = {
            integration: ALTENAR_CONFIG.INTEGRATION,
            culture: ALTENAR_CONFIG.LANG,
            token: isAuthenticated && accessToken ? accessToken : undefined,
          };

          window.altenarWSDK.init(initConfig);

          if (window.ASB && typeof window.ASB.remove === "function") {
            window.ASB.remove();
          }

          // Add sportsbook widget with authentication context
          const sportsbookConfig: any = {
            props: {
              page: "overview",
              championshipIds: [2936, 3146],
              categoryIds: [1134],
              sportId: 66,
            },
            container: document.getElementById("altenarsportsbook"),
            integration: ALTENAR_CONFIG.INTEGRATION,
          };

          // Add user context to sportsbook if authenticated
          if (isAuthenticated && accessToken) {
            console.log(
              "Adding user token context to sportsbook:",
              accessToken
            );
            sportsbookConfig.token = accessToken;
          }

          window.ASB = window.altenarWSDK.addSportsBook(sportsbookConfig);
        } else {
          setTimeout(tryInit, 100);
        }
      }
      tryInit();
    }

    return () => {
      if (window.ASB && typeof window.ASB.remove === "function") {
        window.ASB.remove();
      }
    };
  }, [isAuthenticated, accessToken, userProfile]);

  return (
    
    <div
      id="altenarsportsbook"
      style={{
        paddingBottom: 70,
        paddingTop: 5,
        borderRadius: 0,
        overflow: "auto",
      }}
    >
    </div>
  );
};

export default SportsbookPage;
