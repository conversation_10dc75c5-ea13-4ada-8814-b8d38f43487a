import { useState, useEffect } from 'react';
interface UseFetchOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
  headers?: HeadersInit;
}
const useFetch = (url: string, options?: UseFetchOptions) => {
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const fetchData = async (url: string, options?: UseFetchOptions) => {
    setLoading(true);
    setError(null); // Reset error before a new fetch
    try {
      const response = await fetch(url, {
        method: options?.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(options?.headers || {}),
        },
        body: options?.body ? JSON.stringify(options.body) : undefined,
      });
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      const result = await response.json();
      // console.log("This is result",result)
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  // Fetch data when the URL or options change
  useEffect(() => {
    if (url) {
      fetchData(url, options);
    }
  }, [url, options?.method]); // Consider only including method if it changes
  return { data, error, loading, fetchData }; // Return fetchData to call it manually
};
export default useFetch;