import React from 'react';
import { X } from 'lucide-react';

interface BannerAdProps {
  onClose: () => void;
}

const BannerAd: React.FC<BannerAdProps> = ({ onClose }) => {
  return (
    <div className="fixed bottom-[4.5rem] left-0 right-0 bg-white p-2 border-t border-gray-200 animate-fade-in z-40">
      <div className="max-w-md mx-auto">
        <div className="relative bg-white rounded shadow-lg overflow-hidden">
          {/* Ad Label */}
          <div className="absolute top-1 left-1 bg-black/60 px-1.5 py-0.5 rounded text-[10px] text-white/80 uppercase tracking-wide">
            Advertisement
          </div>
          
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-1 right-1 bg-black/60 p-1 rounded-full hover:bg-black/80 transition-colors"
          >
            <X size={12} className="text-white" />
          </button>

          {/* Ad Content */}
          <div className="flex items-center">
            {/* Ad Image */}
            <div className="w-24 h-24 flex-shrink-0">
              <img
                src="https://images.unsplash.com/photo-1612547036242-77002603e5aa?w=200&h=200&fit=crop"
                alt="Ad"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Ad Copy */}
            <div className="flex-1 p-3">
              <div className="text-xs text-blue-600 font-semibold mb-1">Sponsored</div>
              <h3 className="text-sm font-bold text-gray-900 mb-1">
                Play & Win Big! 🎮
              </h3>
              <p className="text-xs text-gray-600 mb-2">
                Join millions of players worldwide. Start with 5000 free coins!
              </p>
              <button
                onClick={() => window.open('https://example.com', '_blank')}
                className="bg-green-500 text-white text-xs font-bold px-3 py-1.5 rounded hover:bg-green-600 transition-colors"
              >
                PLAY NOW ▶
              </button>
            </div>
          </div>

          {/* Ad Footer */}
          <div className="bg-gray-50 px-3 py-1 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <span className="text-[10px] text-gray-400">ads by AdNetwork</span>
              <button className="text-[10px] text-gray-400 hover:text-gray-600">
                Report this ad
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerAd;