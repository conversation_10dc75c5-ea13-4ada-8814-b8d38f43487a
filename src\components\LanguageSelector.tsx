import React, { useState } from 'react';
import { Globe } from 'lucide-react';

interface Language {
  code: string;
  name: string;
  nativeName: string;
}

const LANGUAGES: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'yo', name: 'Yoruba', nativeName: 'Yorùb<PERSON>' },
  { code: 'ha', name: '<PERSON>usa', nativeName: 'Hausa' },
  { code: 'ig', name: 'Ig<PERSON>', nativeName: 'Igbo' },
  { code: 'pcm', name: 'Nigerian Pidgin', nativeName: 'Pidgin' }
];

const LanguageSelector = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLang, setSelectedLang] = useState<Language>(LANGUAGES[0]);

  const handleLanguageChange = (language: Language) => {
    setSelectedLang(language);
    setIsOpen(false);
    // Here you would typically handle the language change in your app
    // For example, updating context or triggering translations
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-2 rounded-lg hover:bg-white/10 transition-colors"
      >
        <Globe size={20} className="text-white/60" />
        <span className="text-sm">{selectedLang.code.toUpperCase()}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-48 bg-surface-card rounded-xl shadow-lg border border-white/10 overflow-hidden z-50">
          {LANGUAGES.map(lang => (
            <button
              key={lang.code}
              onClick={() => handleLanguageChange(lang)}
              className={`w-full px-4 py-2 text-left hover:bg-white/10 transition-colors flex items-center justify-between ${
                selectedLang.code === lang.code ? 'bg-white/5' : ''
              }`}
            >
              <span>{lang.nativeName}</span>
              <span className="text-sm text-white/40">{lang.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;