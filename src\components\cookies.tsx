import React, { useState, useEffect } from 'react';

const CookieConsent = () => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) setShow(true);
  }, []);

  const handleConsent = (accepted: boolean) => {
    localStorage.setItem('cookieConsent', accepted ? 'accepted' : 'rejected');
    setShow(false);
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 sm:left-auto sm:right-4 z-50 max-w-md mx-auto bg-white rounded-xl shadow-lg p-4 border border-gray-200">
      <p className="text-sm text-gray-700 mb-3">
        We use cookies to improve your experience. By accepting, you agree to our use of cookies. You can always change your preferences.
      </p>
      <div className="flex justify-end gap-2">
        <button
          onClick={() => handleConsent(false)}
          className="px-4 py-2 rounded-lg text-sm bg-gray-200 hover:bg-gray-300 text-gray-800 transition"
        >
          Reject
        </button>
        <button
          onClick={() => handleConsent(true)}
          className="px-4 py-2 rounded-lg text-sm bg-yellow-400 hover:bg-yellow-500 text-black font-semibold transition"
        >
          Accept
        </button>
      </div>
    </div>
  );
};

export default CookieConsent;
