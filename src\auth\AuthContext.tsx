import React, { createContext, useContext, useState,useEffect } from 'react';
import { GET_USER_PROFILE } from '../api/auth';
import { UserProfile } from '../types/userProfile';
/**
 * AuthContextType defines the shape of the authentication context.
 * @property {boolean} isAuthenticated - Whether the user is logged in
 * @property {string | null} accessToken - JWT or bearer token for API requests
 * @property {(token: string) => void} login - Function to log in and store token
 * @property {() => void} logout - Function to log out and clear token
 */
interface AuthContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
    userProfile: UserProfile | null;
  login: (token: string) => void;
  logout: () => void;
  loadUserProfile: () => Promise<void>;
}

/**
 * Create the AuthContext with default values.
 * Consumers should use useAuth() hook to access context.
 */
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  accessToken: null,
    userProfile: null,
  login: () => {},
  logout: () => {},
    loadUserProfile: async() => {}
});

/**
 * Custom hook to access authentication context.
 * @returns {AuthContextType}
 */
export const useAuth = () => useContext(AuthContext);

/**
 * AuthProvider component
 * Wrap your application to provide authentication state.
 *
 * @param {{ children: React.ReactNode }} props - Child components
 * @returns {JSX.Element}
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  /**
   * accessToken state is initialized from localStorage if available.
   */
  const [accessToken, setAccessToken] = useState<string | null>(() => {
    return window.localStorage.getItem('access_token');
  });


    /**
   * userProfile state is initialized from localStorage if available.
   */
  const [userProfile, setUserProfile] = useState<UserProfile | null>(() => {
    const stored = localStorage.getItem('user_profile');
    return stored ? JSON.parse(stored) as UserProfile : null;
  });


  /**
   * automatically fetch userProfile from and store in localStorage when accessToken is set
   */
  useEffect(() => {
    if (accessToken && userProfile === null) {
      loadUserProfile();
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessToken]);

  /**
   * login: save token to localStorage and update state
   * @param {string} token - The access token to store
   */
  const login = (token: string) => {
    window.localStorage.setItem('access_token', token);
    setAccessToken(token);
  };

  /**
   * logout: remove token from localStorage and update state
   */
  const logout = () => {
    window.localStorage.removeItem('access_token');
    setAccessToken(null);
  };
 /**
   * loadUserProfile: fetch user profile information and update state
   */
  const loadUserProfile = async () => {
    if (!accessToken) return;

    try {
      const response = await fetch(GET_USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) throw new Error('Failed to fetch user profile');

      const profile = await response.json();
      console.log("profile",profile)
      setUserProfile(profile);
      localStorage.setItem('user_profile', JSON.stringify(profile));
    } catch (err) {
      console.error('Error loading profile:', err);
      setUserProfile(null); 
      localStorage.removeItem('user_profile');
    }
  };
  /**
   * Context value to pass down
   */
  const value: AuthContextType = {
    isAuthenticated: !!accessToken,
    accessToken,
    login,
    logout,
    userProfile,
    loadUserProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
