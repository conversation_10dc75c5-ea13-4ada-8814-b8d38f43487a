import React, { useState } from 'react';
import { X, Smartphone, Mail, Shield, CheckCircle, Copy, RefreshCw } from 'lucide-react';

interface TwoFactorAuthProps {
  onClose: () => void;
  data: {
    phone_number: string;
  }
}

const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({ onClose, data }) => {
  const [step, setStep] = useState<'method' | 'setup' | 'verify' | 'success'>('method');
  const [method, setMethod] = useState<'app' | 'sms' | null>(null);
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [recoveryKeys, setRecoveryKeys] = useState([
    'ABCD-EFGH-IJKL-MNOP',
    'QRST-UVWX-YZAB-CDEF',
    'GHIJ-KLMN-OPQR-STUV',
    'WXYZ-1234-5678-9012',
  ]);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.querySelector(`input[name="code-${index + 1}"]`) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="code-${index - 1}"]`) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }
  };

  const copyRecoveryKeys = () => {
    navigator.clipboard.writeText(recoveryKeys.join('\n'));
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col transform transition-all duration-300 ease-out">
        <div className="p-6 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-bold">Two-Factor Authentication</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6">
          {/* Method Selection */}
          {step === 'method' && (
            <div className="space-y-6">
              <p className="text-white/60">
                Add an extra layer of security to your account by enabling two-factor authentication.
              </p>

              <div className="space-y-4">
                <button
                  onClick={() => {
                    setMethod('app');
                    setStep('setup');
                  }}
                  className="w-full flex items-center gap-4 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors text-left"
                >
                  <div className="bg-purple-500/20 p-3 rounded-lg">
                    <Smartphone size={24} className="text-purple-400" />
                  </div>
                  <div>
                    <p className="font-medium">Authenticator App</p>
                    <p className="text-sm text-white/60">
                      Use Google Authenticator or similar apps
                    </p>
                  </div>
                </button>

                <button
                  onClick={() => {
                    setMethod('sms');
                    setStep('setup');
                  }}
                  className="w-full flex items-center gap-4 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors text-left"
                >
                  <div className="bg-blue-500/20 p-3 rounded-lg">
                    <Mail size={24} className="text-blue-400" />
                  </div>
                  <div>
                    <p className="font-medium">SMS Authentication</p>
                    <p className="text-sm text-white/60">
                      Receive codes via text message
                    </p>
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Setup Step */}
          {step === 'setup' && (
            <div className="space-y-6">
              {method === 'app' ? (
                <>
                  <div className="text-center">
                    <div className="bg-white p-4 rounded-lg inline-block mb-4">
                      <img 
                        src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=otpauth://totp/Playzuzu:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Playzuzu"
                        alt="QR Code"
                        className="w-32 h-32"
                      />
                    </div>
                    <p className="text-sm text-white/60 mb-4">
                      Scan this QR code with your authenticator app
                    </p>
                    <div className="flex items-center justify-center gap-2 bg-white/5 p-2 rounded-lg">
                      <code className="font-mono text-[#ED0CFF]">JBSWY3DPEHPK3PXP</code>
                      <button className="text-white/60 hover:text-white">
                        <Copy size={16} />
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="space-y-4">
                  <p className="text-white/60">
                    Your verified phone number will be used to receive verification codes via SMS.
                  </p>
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number</label>
                    <input
                      type="tel"
                      value={data.phone_number}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-[#ED0CFF]"
                      placeholder="+****************"
                      readOnly
                    />
                    <p className="text-xs text-white/50 mt-2">
                      Want to use a different number? <span className="text-[#ED0CFF] underline cursor-pointer" onClick={onClose}>Change your number in your profile settings</span>.
                    </p>
                  </div>
                </div>
              )}

              <button
                onClick={() => setStep('verify')}
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Continue
              </button>
            </div>
          )}

          {/* Verification Step */}
          {step === 'verify' && (
            <div className="space-y-6">
              <p className="text-white/60">
                Enter the 6-digit code from your {method === 'app' ? 'authenticator app' : 'SMS message'}.
              </p>

              <div className="flex justify-center gap-2 mb-6">
                {verificationCode.map((digit, index) => (
                  <input
                    key={index}
                    type="text"
                    name={`code-${index}`}
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleCodeChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className="w-12 h-12 text-center bg-white/5 border border-white/10 rounded-lg text-xl font-mono focus:outline-none focus:border-yellow-400"
                  />
                ))}
              </div>

              <div className="flex items-center justify-center gap-2 text-sm">
                <RefreshCw size={16} className="text-white/60" />
                <button className="text-[#ED0CFF] hover:text-[#d30ae0]">
                  Resend Code
                </button>
              </div>

              <button
                onClick={() => setStep('success')}
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Verify
              </button>
            </div>
          )}

          {/* Success Step */}
          {step === 'success' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle size={32} className="text-green-400" />
                </div>
                <h3 className="text-xl font-bold mb-2">2FA Enabled!</h3>
                <p className="text-white/60">
                  Two-factor authentication has been successfully enabled for your account.
                </p>
              </div>

              <div className="bg-white/5 rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <Shield size={18} className="text-[#ED0CFF]" />
                    Recovery Keys
                  </h4>
                  <button
                    onClick={copyRecoveryKeys}
                    className="text-sm text-[#ED0CFF] hover:text-[#d30ae0] flex items-center gap-1"
                  >
                    <Copy size={14} />
                    Copy All
                  </button>
                </div>
                <p className="text-sm text-white/60 mb-4">
                  Save these recovery keys in a secure place. You can use them to regain access to your account if you lose your authentication device.
                </p>
                <div className="grid grid-cols-2 gap-2">
                  {recoveryKeys.map((key, index) => (
                    <div
                      key={index}
                      className="bg-white/10 p-2 rounded-lg font-mono text-sm text-center"
                    >
                      {key}
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={onClose}
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Done
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TwoFactorAuth;
