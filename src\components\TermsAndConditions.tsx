import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const TermsAndConditionsPage: React.FC = () => {

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);


  return (
    <div className="min-h-screen w-full    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] p-6 text-white">
      {/* Go Back Arrow to Home */}
      <header className="mb-8 flex items-center">
        <Link to="/" className="flex items-center gap-2 text-white hover:text-gray-300">
          <ArrowLeft size={24} />
        </Link>
          <div className='flex items-center justify-center  w-full'>
            <h1 className="text-3xl tracking-wider  text-center font-[Anton]">Terms and Conditions</h1>
          </div>
      </header>

      <main className="max-w-3xl mx-auto space-y-8">
        {/* <h1 className="text-4xl font-bold text-center font-[<PERSON>]">Terms and Conditions</h1> */}

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">1. Acceptance of Terms</h2>
          <p className="text-base leading-relaxed">
            By accessing and using Playzuzu, you agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you must not use our service.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">2. Age Restrictions</h2>
          <p className="text-base leading-relaxed">
            You must be at least 18 years old to use Playzuzu. By using our service, you represent and warrant that you are at least 18 years of age.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">3. Virtual Currency</h2>
          <p className="text-base leading-relaxed">
            Coins, FPP, and other virtual items have no real-world value and cannot be exchanged for real money. These virtual items are licensed, not sold, to you.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">4. User Conduct</h2>
          <ul className="list-disc list-inside text-base space-y-2">
            <li>Attempt to manipulate or exploit game mechanics</li>
            <li>Use any automated systems or bots</li>
            <li>Share accounts with other users</li>
            <li>Engage in any fraudulent activities</li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">5. Account Termination</h2>
          <p className="text-base leading-relaxed">
            We reserve the right to terminate or suspend accounts that violate these terms, without prior notice or liability.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">6. Changes to Terms</h2>
          <p className="text-base leading-relaxed">
            We may modify these terms at any time. Continued use of Playzuzu after changes constitutes acceptance of the modified terms.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-2  font-[Anton]">7. Limitation of Liability</h2>
          <p className="text-base leading-relaxed">
            Playzuzu is provided "as is" without warranties of any kind. We are not liable for any damages arising from your use of our service.
          </p>
        </section>
      </main>
    </div>
  );
};

export default TermsAndConditionsPage;
